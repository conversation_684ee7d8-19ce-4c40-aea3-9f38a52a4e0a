<!DOCTYPE html>
<html>
<head>
  <title>大锋插件管理器</title>
  <meta charset="UTF-8">
  <style>
:root {
      --bg-color: #f0f0f0; --panel-bg-color: #ffffff; --panel-header-bg-color: #f7f7f7; --text-color: #333; --border-color: #ddd; --item-border-color: #ccc; --item-bg-color: #f9f9f9; --btn-hover-color: #d1deeb; --accent-color: #0078D7;
    }
    /* 暗色模式已移除，默认使用白天模式 */
    body, html {
      height: 100%;
      margin: 0;
      padding: 0;
      overflow: hidden; /* 完全隐藏所有滚动条 */
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      background-color: var(--bg-color);
      color: var(--text-color);
    }

    /* 确保所有容器都不产生滚动条 */
    * {
      box-sizing: border-box;
    }

    /* 强制隐藏所有可能的底部滚动条 */
    body::-webkit-scrollbar,
    html::-webkit-scrollbar,
    .app-container::-webkit-scrollbar,
    .main-container::-webkit-scrollbar,
    .panel::-webkit-scrollbar,
    #source-panel::-webkit-scrollbar {
      display: none !important;
      width: 0 !important;
      height: 0 !important;
    }

    /* 确保主要容器不会产生滚动条 */
    body, html, .app-container, .main-container, .panel, #source-panel {
      overflow-x: hidden !important;
      overflow-y: hidden !important;
    }

    /* 只允许内容区域滚动 */
    .panel-content {
      overflow-x: hidden !important;
      overflow-y: auto !important;
    }

    /* 右下角垂直拉伸手柄 - 隐藏图标但保留功能 */
    .resize-handle {
      position: fixed;
      bottom: 0;
      right: 0;
      width: 20px;
      height: 20px;
      background: transparent; /* 完全透明，隐藏图标 */
      cursor: ns-resize; /* 只允许垂直调整 */
      z-index: 1000;
    }
    .app-container {
      display: flex;
      flex-direction: column;
      height: 100%;
      box-sizing: border-box;
      padding: 0;
      overflow: hidden; /* 确保容器本身不产生滚动条 */
    }
    .header {
      flex-shrink: 0;
      height: 60px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px 8px 12px;
      background-color: var(--panel-bg-color);
      position: relative;
    }

    .header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background-color: var(--border-color);
    }

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
      min-width: 0; /* 允许内容收缩 */
    }

    .header-icon {
      width: 32px;
      height: 32px;
      border-radius: 5px;
      background: linear-gradient(135deg, #4a90e2, #357abd);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 16px;
      /* box-shadow: 0 2px 4px rgba(0,0,0,0.1); */ /* 移除阴影，简约风格 */
      overflow: hidden;
      flex-shrink: 0;
    }

    .header-icon img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 5px;
    }

    .header-info {
      display: flex;
      flex-direction: column;
      gap: 1px;
      min-width: 0; /* 允许内容收缩 */
      flex: 1;
    }

    .header-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--text-color);
      margin: 0;
      line-height: 1.2;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .header-subtitle {
      font-size: 11px;
      color: #666;
      margin: 0;
      line-height: 1.2;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    /* 暗色模式样式已移除 */

    .header-right {
      display: none; /* 隐藏空的右侧区域 */
    }


    .main-container {
      display: flex;
      flex-direction: column;
      flex-grow: 1;
      gap: 0;
      overflow: hidden;
      margin-top: 0;
      height: 100%; /* 占满剩余空间 */
      max-height: 100%; /* 防止超出 */
    }
    .panel {
      display: flex;
      flex-direction: column;
      background-color: var(--panel-bg-color);
      border: none; /* 移除边框，简约风格 */
      border-radius: 0;
      overflow: hidden;
      height: 100%; /* 确保面板占满容器高度 */
    }

    .panel-content {
      padding: 0 0 10px 12px; /* 上padding为0，右padding为0，下padding为10px，左padding为12px（与header保持一致） */
      overflow-y: auto;
      overflow-x: hidden; /* 确保不出现水平滚动条 */
      flex-grow: 1;
      position: relative;
      height: 0; /* 让flex-grow生效 */
      min-height: 0; /* 防止内容撑开容器 */
    }
    #source-panel {
      width: 100%;
      overflow: hidden; /* 确保主面板不产生滚动条 */
    }
    button { padding: 6px 12px; font-size: 0.9em; border: none; border-radius: 0; cursor: pointer; }
    .primary-btn { background-color: var(--accent-color); color: white; }
    .primary-btn:hover { filter: brightness(1.1); }
    input[type="text"] { background-color: var(--panel-bg-color); color: var(--text-color); border: 1px solid var(--border-color); padding: 5px; border-radius: 0; }

    /* 搜索容器样式 */
    .search-container {
      position: relative;
      width: 100%;
      max-width: 280px;
    }

    /* 搜索输入框样式 */
    .search-container input[type="text"] {
      width: 100%;
      padding: 10px 40px 10px 16px;
      border: none; /* 移除边框，简约风格 */
      border-radius: 0;
      background-color: #f8f9fa;
      color: var(--text-color);
      font-size: 14px;
      outline: none;
      transition: all 0.2s ease;
      box-sizing: border-box;
    }

    .search-container input[type="text"]:focus {
      background-color: #fff;
      /* box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1); */ /* 移除阴影，简约风格 */
    }

    .search-container input[type="text"]::placeholder {
      color: #6c757d;
      font-size: 14px;
    }

    /* 搜索图标样式 */
    .search-icon {
      position: absolute;
      right: 14px;
      top: 50%;
      transform: translateY(-50%);
      color: #6c757d;
      font-size: 16px;
      pointer-events: none;
    }

    /* 面板头部样式调整 */
    .panel-header {
      padding: 16px 20px;
      border-bottom: none; /* 移除边框，简约风格 */
      background-color: var(--panel-header-bg-color);
      flex-shrink: 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    /* 搜索框暗色模式样式已移除 */

    /* 分组样式 */
    .group-header {
      position: relative; /* 为伪元素定位 */
      padding: 10px 0; /* 上下padding保持10px，左右padding设置为0 */
      background-color: #e8e8e8; /* 参考图片的浅灰色背景 */
      border: none; /* 移除原有边框 */
      font-weight: 600;
      font-size: 13px;
      color: #333;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center; /* 分组名字居中 */
      user-select: none;
      transition: background-color 0.2s ease;
      margin-bottom: 0px;
      text-align: center; /* 文本居中 */
    }

    /* 分组标题的底部分隔线，延伸到界面边框 */
    .group-header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -12px; /* 延伸到界面左边框：抵消panel-content左padding(12px) */
      right: 0; /* 延伸到界面右边框：panel-content右padding为0，无需抵消 */
      height: 1px;
      background-color: #ddd;
    }

    .group-header:hover {
      background-color: #d0d0d0; /* 悬停时稍微深一点的灰色 */
    }

    /* 使用伪元素创建完全贯通的背景 */
    .group-header:hover::before {
      content: '';
      position: absolute;
      top: 0;
      left: -12px; /* 扩展到界面左边缘：抵消panel-content的左padding(12px) */
      right: 0; /* 扩展到界面右边缘：panel-content右padding为0，直接到边缘 */
      bottom: 0;
      background-color: #d0d0d0; /* 与悬停背景色保持一致 */
      z-index: -1;
    }

    .group-expand-indicator {
      display: none; /* 隐藏分组三角图标 */
    }

    .group-title {
      flex: 1;
      font-size: 13px;
    }

    .group-container {
      margin-bottom: 8px;
    }

    /* 工具栏项目样式调整 */
    .toolbar-item {
      margin-left: 16px;
      border-left: none; /* 移除边框，简约风格 */
    }

    .toolbar-name-header {
      padding: 6px 0; /* 上下padding保持6px，左右padding设置为0 */
      display: flex;
      align-items: center;
      cursor: pointer;
      user-select: none;
      transition: background-color 0.2s ease;
      font-size: 12px;
      border-radius: 0; /* 确保无圆角 */
      background-color: #e8e8e8; /* 参考图片的浅灰色背景 */
    }

    .toolbar-name-header:hover {
      background-color: #d0d0d0; /* 悬停时稍微深一点的灰色 */
    }

    /* 使用伪元素创建扩展的背景，避免闪烁 */
    .toolbar-name-header:hover::before {
      content: '';
      position: absolute;
      top: 0;
      left: -28px; /* 抵消 panel-content(12px) + toolbar-item margin(16px) */
      right: -20px; /* 抵消 panel-content(10px) + toolbar-item padding(10px) */
      bottom: 0;
      background-color: #d0d0d0; /* 与悬停背景色保持一致 */
      z-index: -1;
      border-radius: 0; /* 确保背景无圆角 */
    }

    .toolbar-name {
      flex: 1;
      margin-left: 6px;
      font-size: 12px;
      color: #555;
    }

    .toolbar-status {
      color: #28a745;
      font-size: 8px;
      margin-left: 6px;
    }

    .expand-indicator {
      font-size: 8px;
      transition: transform 0.2s ease;
      color: #888;
    }

    /* 暗色模式样式已移除，以下样式不再使用 */
    body.dark-mode .group-header {
      background-color: #3a3a3a; /* 暗色模式下的深灰色背景 */
      color: #e0e0e0;
    }

    body.dark-mode .group-header::after {
      background-color: #555; /* 暗色模式下的分隔线颜色 */
    }

    body.dark-mode .group-header:hover {
      background-color: #4a4a4a; /* 暗色模式下悬停时稍微亮一点 */
    }

    body.dark-mode .group-header:hover::before {
      content: '';
      position: absolute;
      top: 0;
      left: -12px; /* 扩展到界面左边缘：抵消panel-content的左padding(12px) */
      right: 0; /* 扩展到界面右边缘：panel-content右padding为0，直接到边缘 */
      bottom: 0;
      background-color: #4a4a4a; /* 与暗色模式悬停背景色保持一致 */
      z-index: -1;
    }

    body.dark-mode .group-container {
      border-bottom-color: #555;
    }

    body.dark-mode .toolbar-item {
      border-left-color: #555;
    }

    body.dark-mode .toolbar-name-header {
      background-color: #3a3a3a; /* 暗色模式下的深灰色背景 */
    }

    body.dark-mode .toolbar-name-header:hover {
      background-color: #4a4a4a; /* 暗色模式下悬停时稍微亮一点 */
    }

    body.dark-mode .toolbar-name-header:hover::before {
      content: '';
      position: absolute;
      top: 0;
      left: -28px; /* 抵消 panel-content(12px) + toolbar-item margin(16px) */
      right: -20px; /* 抵消 panel-content(10px) + toolbar-item padding(10px) */
      bottom: 0;
      background-color: #4a4a4a; /* 与暗色模式悬停背景色保持一致 */
      z-index: -1;
      border-radius: 0; /* 确保背景无圆角 */
    }

    body.dark-mode .command-list {
      background-color: #2a2a2a; /* 暗色模式下功能栏使用更深的灰色背景 */
    }

    /* 空分组消息样式 */
    .empty-group-message {
      padding: 16px 20px;
      text-align: center;
      color: #999;
      font-size: 12px;
      font-style: italic;
      border-left: none; /* 移除边框，简约风格 */
      margin-left: 16px;
    }

    body.dark-mode .empty-group-message {
      color: #666;
      border-left-color: #555;
    }

    /* 无数据消息样式 */
    .no-data-message {
      padding: 40px 20px;
      text-align: center;
      color: #999;
      font-size: 14px;
    }

    .no-data-message p {
      margin: 8px 0;
    }

    body.dark-mode .no-data-message {
      color: #666;
    }

    /* 移动到分组模态对话框样式 */
    .move-to-group-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 10000;
    }

    .modal-overlay {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background-color: var(--panel-bg-color);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      min-width: 300px;
      max-width: 400px;
    }

    .modal-header {
      padding: 16px 20px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .modal-header h3 {
      margin: 0;
      font-size: 16px;
      color: var(--text-color);
    }

    .modal-close {
      background: none;
      border: none;
      font-size: 20px;
      cursor: pointer;
      color: var(--text-color);
      padding: 0;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .modal-close:hover {
      background-color: var(--border-color);
      border-radius: 4px;
    }

    .modal-body {
      padding: 20px;
    }

    .modal-body p {
      margin: 0 0 16px 0;
      color: var(--text-color);
      font-size: 14px;
    }

    .group-list {
      max-height: 200px;
      overflow-y: auto;
    }

    .group-option {
      padding: 10px 12px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .group-option:hover {
      background-color: var(--accent-color);
      color: white;
    }

    .group-name {
      font-size: 13px;
    }

    .modal-footer {
      padding: 12px 20px;
      border-top: 1px solid var(--border-color);
      text-align: right;
    }

    .btn-cancel {
      background-color: var(--border-color);
      color: var(--text-color);
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 13px;
    }

    .btn-cancel:hover {
      background-color: #ccc;
    }

    /* 暗色模式下的模态对话框样式 */
    body.dark-mode .modal-close:hover {
      background-color: #555;
    }

    body.dark-mode .btn-cancel {
      background-color: #555;
      color: #e0e0e0;
    }

    body.dark-mode .btn-cancel:hover {
      background-color: #666;
    }

    /* 拖拽手柄样式 - 隐藏视觉显示但保留功能 */
    .drag-handle {
      display: none; /* 隐藏拖拽手柄视觉显示 */
    }

    /* 分组和工具栏拖拽状态 */
    .group-header.dragging,
    .toolbar-name-header.dragging {
      opacity: 0.5;
      transform: rotate(2deg);
    }

    /* 拖拽指示器 */
    .group-header.drag-over-top::before,
    .toolbar-name-header.drag-over-top::before {
      content: '';
      position: absolute;
      top: -2px;
      left: 0;
      right: 0;
      height: 3px;
      background-color: var(--accent-color);
      border-radius: 2px;
      z-index: 1000;
    }

    .group-header.drag-over-bottom::after,
    .toolbar-name-header.drag-over-bottom::after {
      content: '';
      position: absolute;
      bottom: -2px;
      left: 0;
      right: 0;
      height: 3px;
      background-color: var(--accent-color);
      border-radius: 2px;
      z-index: 1000;
    }

    /* 确保分组和工具栏标题有相对定位 */
    .group-header,
    .toolbar-name-header {
      position: relative;
    }
    .tab-bar {
      border-bottom: none; /* 移除边框，简约风格 */
      display: flex; /* 使用Flex布局 */
      justify-content: space-between; /* 两端对齐 */
      align-items: center; /* 垂直居中 */
      flex-shrink: 0;
    }

    .command-list {
      display: flex;
      flex-wrap: wrap;
      gap: 5px; /* 调整间距确保5个图标能够合适排列 */
      min-height: 36px; /* 相应减小最小高度 */
      padding: 5px;
      border-radius: 0;
      /* 使用负边距让容器边缘紧贴界面边框 */
      margin-left: -28px; /* 抵消 panel-content(12px) + toolbar-item margin(16px) */
      margin-right: 0; /* 右边直接到边缘 */
      padding-left: 12px; /* 图标离边框统一距离，与header保持一致 */
      padding-right: 10px; /* 图标离边框统一距离 */
      justify-content: flex-start; /* 图标从左开始排列 */
      background-color: #f0f0f0; /* 功能栏使用稍微浅一点的灰色背景 */
    }
    .command-item {
      position: relative;
      cursor: grab;
      padding: 4px;
      border: none;
      border-radius: 0;
      background-color: var(--item-bg-color);
      transition: all 0.2s ease;
      text-align: left; /* 插件名字左边对齐 */
      display: flex;
      align-items: center;
      flex: 0 0 auto; /* 防止图标被拉伸 */
      width: 36px; /* 固定宽度：28px图标 + 8px padding */
      height: 36px; /* 固定高度：28px图标 + 8px padding */
      justify-content: center; /* 图标在容器中居中 */
    }

    /* 命令项目的底部分隔线，延伸到界面边缘 - 已删除 */
    /* .command-item::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -25px;
      right: -25px;
      height: 1px;
      background-color: var(--border-color);
    } */
    .command-item:hover { background-color: var(--btn-hover-color); /* 移除边框和阴影，简约风格 */ }
    .command-item img { width: 28px; height: 28px; display: block; pointer-events: none; }
    .command-list .separator {
      position: relative; /* 为指示器伪元素定位做准备 */
      width: 2px;
      height: 28px; /* 与图标高度保持一致 */
      background-color: var(--item-border-color);
      border-radius: 1px;
      /* 增大水平外边距，给予更多可点击空间 */
      margin: 4px 0px; 
      flex-shrink: 0;
      cursor: grab; /* 鼠标悬停时显示为“可抓取”手型 */
    }
    .dragging { opacity: 0.4; }
    .custom-toolbars-container { flex-grow: 1; overflow-y: auto; padding: 10px; }
    .toolbar-item {
      position: relative;
      margin-bottom: 0px;
      padding: 0; /* 所有padding都设置为0 */
      border: none;
      border-radius: 0;
      background-color: var(--panel-bg-color);
      transition: border-color 0.2s ease;
      /* 使用负边距让容器边缘紧贴界面边框 */
      margin-left: -12px; /* 抵消 panel-content(12px) */
      margin-right: 0; /* 右边直接到边缘 */
    }

    /* 工具栏项目的顶部分隔线，延伸到界面边框 */
    .toolbar-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: -12px; /* 延伸到界面边框：panel-content(12px) */
      right: 0; /* 延伸到界面右边框：panel-content右padding为0，直接到边缘 */
      height: 1px;
      background-color: var(--border-color);
    }

    /* 工具栏项目的底部分隔线，延伸到界面边框 - 已删除 */
    /* .toolbar-item::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -10px;
      right: -10px;
      height: 1px;
      background-color: var(--border-color);
    } */
    .toolbar-item.expanded { border-color: var(--accent-color); }
    .toolbar-name-header {
      position: relative; /* 为伪元素定位 */
      padding-bottom: 10px;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    /* 插件栏的底部分隔线，延伸到界面边框 - 已删除 */
    /* .toolbar-name-header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: -20px;
      right: -20px;
      height: 1px;
      background-color: var(--border-color);
    } */
    .collapsible-header { cursor: pointer; padding: 8px 12px; border-radius: 0; transition: background-color 0.2s ease; }
    .collapsible-header:hover { background-color: var(--btn-hover-color); }
    .expand-indicator { display: none; /* 隐藏工具栏三角图标 */ }
    .toolbar-item.expanded .expand-indicator { transform: rotate(90deg); }
    .toolbar-name { user-select: none; }
    .item-actions { display: flex; align-items: center; gap: 8px; }
    .item-actions button, .item-actions select { font-size: 0.8em; padding: 3px 6px; background-color: var(--item-bg-color); border: none; /* 移除边框，简约风格 */ color: var(--text-color); border-radius: 3px; }
    .toolbar-footer {
      display: flex;
      gap: 10px;
      padding: 10px;
      border-top: none;
      flex-shrink: 0;
      position: relative;
    }

    /* 工具栏底部的分隔线，延伸到边缘 */
    .toolbar-footer::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background-color: var(--border-color);
    }
    #radial-menus-content { flex-direction: column; height: 100%; }
    .radial-actions-bar {
      padding: 10px;
      flex-shrink: 0;
      border-bottom: none;
      position: relative;
    }

    /* radial-actions-bar的底部分隔线，延伸到边缘 */
    .radial-actions-bar::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background-color: var(--border-color);
    }
    .radial-actions-bar button { margin-right: 10px; }
    .radial-list-container { overflow-y: auto; flex-grow: 1; padding: 10px; }
    .disk-container, .group-container {
      position: relative;
      background-color: var(--panel-bg-color);
      border: none;
      border-radius: 0;
      margin-bottom: 10px;
    }

    /* 分组容器的顶部分隔线，延伸到边缘 - 已移除 */
    /* .disk-container::before, .group-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1px;
      background-color: var(--border-color);
    } */

    /* 分组容器的底部分隔线，延伸到边缘 */
    .disk-container::after, .group-container::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background-color: var(--border-color);
    }
    .disk-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: bold;
      padding: 10px;
      cursor: pointer;
      background-color: var(--item-bg-color);
      border-radius: 0;
      position: relative;
    }

    /* 展开状态下的disk-header底部分隔线，延伸到边缘 */
    .disk-container.is-expanded .disk-header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background-color: var(--border-color);
    }
    .group-header { font-weight: bold; padding: 10px 0; /* 上下padding保持10px，左右padding设置为0 */ background-color: #e8e8e8; /* 参考图片的浅灰色背景 */ display: flex; font-size: 0.9em; justify-content: center; align-items: center; text-align: center; }
    .group-container { border: none; padding: 0; /* 移除padding，简约风格 */ }
    .disk-list-in-group { margin-top: 5px; }
    .toggle-expand-btn::after { content: '▶'; display: inline-block; margin-left: 8px; transition: transform 0.2s; font-size: 0.7em; }
    .disk-container.is-expanded .toggle-expand-btn::after { transform: rotate(90deg); }
    .disk-body { display: none; }
    .disk-container.is-expanded .disk-body { display: block; }
    .disk-container.is-expanded .linear-preview {
        display: none;
    }
    .linear-preview { display: flex; flex-wrap: wrap; gap: 4px; padding: 10px; }
    .linear-preview .command-item { cursor: default; }
    #source-panel.delete-zone-active { /* 移除边框和阴影，简约风格 */ }
    #source-list.delete-zone-active::before {
      content: '删除图标';
      position: absolute;
      top: 0; left: 0; right: 0; bottom: 0;
      background-color: rgba(220, 53, 69, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 1.5em;
      color: #dc3545;
      font-weight: bold;
      z-index: 100;
      pointer-events: none; /* 确保不干扰鼠标事件 */
      backdrop-filter: blur(1px);
    }
    .command-item.in-slot { cursor: grab; }

    /* --- 【新增】模拟圆盘编辑器的样式 --- */
    .radial-editor {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 380px; /* 确保有足够的高度来容纳最大的环 */
      padding: 20px;
      box-sizing: border-box;
    }
    .editor-rings-container {
      position: relative;
      width: 340px; /* 外圈直径 */
      height: 340px; /* 外圈直径 */
    }
    .editor-ring {
      position: absolute;
      top: 50%;
      left: 50%;
      border: none; /* 移除边框，简约风格 */
      border-radius: 50%;
      transform: translate(-50%, -50%);
      pointer-events: none; /* 【修复】让鼠标可以“穿透”这个容器 */
    }
    .command-slot {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 42px; /* 包含边框的总宽度 */
      height: 42px; /* 包含边框的总高度 */
      margin: -21px 0 0 -21px; /* (width/2), (height/2) */
      background-color: var(--bg-color);
      border: none; /* 移除边框，简约风格 */
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      pointer-events: auto;
    }
    .command-slot.drag-over {
      /* 移除阴影和边框，简约风格 */
      background-color: var(--btn-hover-color); /* 用背景色表示悬停状态 */
    }
    /* --- 【新增】快捷键按钮的差异化样式 --- */
    .item-actions button.has-shortcut {
      background-color: #d4edda; /* 淡绿色背景 */
      color: #155724; /* 深绿色文字 */
      border-color: #c3e6cb;
      font-weight: bold;
    }
    
    /* .item-actions button.no-shortcut {  */
    /* background-color: #f8d7da; /* 淡红色背景 */
    /* color: #721c24; /* 深红色文字 */
    /* border-color: #f5c6cb;
    /* }  */
    /* --- 【新增】自定义右键菜单的样式 --- */
    #context-menu {
      background-color: var(--panel-bg-color);
      border: none; /* 移除边框，简约风格 */
      border-radius: 4px;
      /* box-shadow: 0 2px 8px rgba(0,0,0,0.15); */ /* 移除阴影，简约风格 */
      padding: 4px 0;
      min-width: 140px;
      font-size: 12px;
    }
    .context-menu-item {
      padding: 6px 12px;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: background-color 0.2s ease;
    }
    .context-menu-item:hover {
      background-color: var(--accent-color);
      color: white;
    }
    .context-menu-separator {
      height: 1px;
      background-color: var(--border-color);
      margin: 3px 0;
    }
    .drag-over-top::before,
    .drag-over-bottom::after {
      content: '';
      position: absolute;
      left: 10px;  /* 增加一些左边距，更美观 */
      right: 10px; /* 增加一些右边距，更美观 */
      height: 3px;
      background-color: var(--accent-color);
      border-radius: 2px;
      z-index: 10;
    }
    .drag-over-top::before {
      top: -2px;
    }
    .drag-over-bottom::after {
      bottom: -2px;
    }
    .group-container.drag-over-inside {
      background-color: var(--accent-color) !important;
      opacity: 0.2;
    }
    
    /* is-dragging 样式保持不变 */
    .is-dragging {
      opacity: 0.5;
    }
    /* 为所有会滚动的容器定义滚动条的宽度 - 覆盖在内容上 */
    .panel-content::-webkit-scrollbar,
    .custom-toolbars-container::-webkit-scrollbar,
    .radial-list-container::-webkit-scrollbar,
    #persistent-toolbars-content::-webkit-scrollbar { /* <-- 新增此行选择器 */
      width: 4px; /* 进一步缩小滚动条宽度，更美观 */
      z-index: 9999; /* 确保滚动条在所有界面元素上方 */
    }

    /* 让滚动条覆盖在内容上，不占用空间 */
    .panel-content,
    .custom-toolbars-container,
    .radial-list-container,
    #persistent-toolbars-content {
      scrollbar-width: thin; /* Firefox滚动条 */
      overflow-y: auto; /* 标准滚动条设置 */
      /* 移除scrollbar-gutter，避免预留空间造成缝隙 */
    }

    /* 强制让滚动条悬浮，不占用布局空间 */
    .panel-content {
      margin-right: 0 !important; /* 确保右边距为0 */
      /* padding-right已在基础样式中设置为0 */
    }
    
    /* 定义滚动条的轨道（背景） - 完全透明 */
    .panel-content::-webkit-scrollbar-track,
    .custom-toolbars-container::-webkit-scrollbar-track,
    .radial-list-container::-webkit-scrollbar-track,
    #persistent-toolbars-content::-webkit-scrollbar-track { /* <-- 新增此行选择器 */
      background: transparent;
      z-index: 9999; /* 确保轨道在所有界面元素上方 */
    }
    
    /* 定义滚动条的滑块（就是那个可以拖动的条） - 覆盖样式 */
    .panel-content::-webkit-scrollbar-thumb,
    .custom-toolbars-container::-webkit-scrollbar-thumb,
    .radial-list-container::-webkit-scrollbar-thumb,
    #persistent-toolbars-content::-webkit-scrollbar-thumb { /* <-- 新增此行选择器 */
      background-color: rgba(160, 160, 160, 0.5); /* 更透明的滑块，更美观 */
      border-radius: 2px; /* 更小的圆角，配合更窄的滚动条 */
      border: none; /* 移除边框，让滑块更紧凑 */
      margin: 0 1px; /* 添加左右边距，让滑块不贴边 */
      z-index: 10000; /* 确保滑块在最上层，高于所有界面元素 */
    }
    
    /* 定义滑块在鼠标悬停时的颜色 */
    .panel-content::-webkit-scrollbar-thumb:hover,
    .custom-toolbars-container::-webkit-scrollbar-thumb:hover,
    .radial-list-container::-webkit-scrollbar-thumb:hover,
    #persistent-toolbars-content::-webkit-scrollbar-thumb:hover { /* <-- 新增此行选择器 */
      background-color: rgba(136, 136, 136, 0.7); /* 悬停时适度不透明，不要太突兀 */
    }
    
    /* 【修复】为深色模式定义独立的滑块颜色 */
    body.dark-mode .panel-content::-webkit-scrollbar-thumb,
    body.dark-mode .custom-toolbars-container::-webkit-scrollbar-thumb,
    body.dark-mode .radial-list-container::-webkit-scrollbar-thumb {
      background-color: rgba(180, 180, 180, 0.4); /* 更透明的浅色滑块，在暗色背景下更美观 */
      border: none; /* 移除边框 */
      border-radius: 2px; /* 与白天模式保持一致 */
      margin: 0 1px; /* 添加左右边距 */
      z-index: 10000; /* 确保暗色模式滑块在最上层，高于所有界面元素 */
    }

    body.dark-mode .panel-content::-webkit-scrollbar-thumb:hover,
    body.dark-mode .custom-toolbars-container::-webkit-scrollbar-thumb:hover,
    body.dark-mode .radial-list-container::-webkit-scrollbar-thumb:hover {
      background-color: rgba(200, 200, 200, 0.6); /* 悬停时适度不透明 */
    }
     /* --- 【核心修改】常驻工具条设置页的最终样式 --- */
    #persistent-toolbars-content {
      display: flex; /* 确保它是一个flex容器 */
      flex-direction: column; /* 让内部分区垂直排列 */
      overflow-y: auto; 
    }
    
    /* 顶部停靠槽的容器 */
    .dock-slots-area {
      padding: 10px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      flex-direction: column; /* 让三个槽垂直排列 */
      gap: 10px; /* 槽之间的间距 */
    }
    
    /* 单个停靠槽的样式，复用 .group-container 的虚线框 */
    .dock-slot {
      border: 2px dashed var(--item-border-color);
      border-radius: 6px;
      padding: 10px;
      min-height: 40px;
      color: var(--text-color);
      font-weight: bold;
      background-color: var(--item-bg-color);
      display: flex;
      flex-direction: column; /* 让未来放入的DOCK栏也垂直排列 */
      gap: 5px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    /* 这是每个插槽的总包裹容器 */
    .dock-slot-wrapper {
      border: 1px dashed var(--item-border-color);
      border-radius: 6px;
      background-color: var(--item-bg-color);
    }

    /* 这是头部区域，专门用来放标题和按钮 */
    .dock-slot-header {
      display: flex;            /* 使用Flex布局 */
      flex-direction: row;      /* 确保是横向排列 */
      justify-content: space-between; /* 两端对齐，标题靠左，按钮靠右 */
      align-items: center;      /* 垂直居中 */
      padding: 8px 12px;
      font-weight: bold;
      color: var(--text-color);
      font-size: 0.9em;
    }
    
    /* 这是内容区域，专门用来放拖入的DOCK栏 */
    .dock-slot-content {
      padding: 0 10px 10px 10px; /* 顶部无内边距，其他方向有 */
      min-height: 40px;
      display: flex;
      flex-direction: column;
      gap: 5px;
    }
    
    /* 当内容区有DOCK栏时，给头部添加一个下边框作为分割线 */
    .dock-slot-header.has-content {
      border-bottom: 1px solid var(--border-color);
      margin-bottom: 10px; 
    }

    /* 刷新按钮的样式 (模仿 "设置快捷键" 按钮) */
    .header-action-btn {
      font-size: 0.9em;
      padding: 3px 8px;
      background-color: var(--item-bg-color);
      border: 1px solid var(--border-color);
      color: var(--text-color);
      border-radius: 4px;
      cursor: pointer;
      flex-shrink: 0;
    }
    .header-action-btn:hover {
      filter: brightness(1.1);
      border-color: var(--accent-color);
    }
    
    /* 下方DOCK栏列表的容器 */
    .dock-bars-list-container {
      flex-grow: 1; /* 占据所有剩余的垂直空间 */
      padding: 10px;
    }
    /* --- 【修改结束】 --- */
    /* 汉堡菜单样式已删除 */
    /* --- 【新增】编辑器三栏布局和设置区的样式 --- */
    .editor-grid {
      display: grid;
      /* 【核心修改】将两侧边栏的宽度从200px缩小到160px */
      grid-template-columns: 150px 1fr 150px; 
      gap: 20px;
      padding: 10px;
      align-items: flex-start;
    }
    
    .quick-action-bar-config, .layout-settings {
      padding: 10px;
      background-color: var(--item-bg-color);
      border-radius: 6px;
    }
    
    .layout-settings h5, .quick-action-bar-config h5 {
      margin-top: 0;
      margin-bottom: 15px;
      text-align: center;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 10px;
    }
    
    /* --- 【新增】滑块控件的样式 --- */
    .slider-control {
      margin-bottom: 15px;
    }
    .slider-control label {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
      font-size: 0.9em;
    }
    .slider-control input[type="range"] {
      width: 100%;
      cursor: pointer;
    }
    /* --- 【新增】切换开关(Toggle Switch)的样式 --- */
    .toggle-control {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 20px;
      margin-right: 20px;
    }
    .toggle-control label {
      font-size: 0.9em;
      padding-right: 5px;
      font-weight: normal;
    }
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 24px; /* 缩小拨钮总宽度 */
      height: 14px; /* 缩小拨钮总高度 */
    }
    .toggle-switch input { 
      opacity: 0;
      width: 0;
      height: 0;
    }
    .toggle-switch .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 14px;
    }
    .toggle-switch .slider:before {
      position: absolute;
      content: "";
      height: 10px; /* 缩小拨钮上圆点的高度 */
      width: 10px; /* 缩小拨钮上圆点的宽度 */
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }
    .toggle-switch input:checked + .slider {
      background-color: var(--accent-color);
    }
    .toggle-switch input:checked + .slider:before {
      transform: translateX(14px); /* 调整圆点移动的距离 */
    }
    /* --- 【替换/新增】快捷启动条的全新布局样式 --- */
    .quick-action-bar-config {
      padding: 10px;
      background-color: var(--item-bg-color);
      border-radius: 6px;
      display: grid; /* 使用网格布局，为未来的多列做准备 */
      grid-template-columns: repeat(2, 1fr); /* 默认两列布局 */
      gap: 8px;
      /* --- 【核心新增】 --- */
      /* 1. 定义网格的填充顺序为“列”优先 */
      grid-auto-flow: column; 
      /* 2. 定义每一列最多有6行 */
      grid-template-rows: repeat(7, auto); 
    }
    
    .quick-action-bar-config h5 {
      margin-top: 0;
      margin-bottom: 10px;
      text-align: center;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 10px;
      grid-column: 1 / -1; /* 让标题横跨所有列 */
    }
    
    .qa-slot {
      /* 插槽容器本身使用相对定位，作为快捷键输入的定位基准 */
      position: relative;
      display: flex; /* 让内部元素（目前只有图标区）能正常显示 */
      height: 42px; /* 固定高度，以便快捷键定位 */
    }
    
    .qa-key-input {
      /* 使用绝对定位，将快捷键输入框放置在右下角 */
      position: absolute;
      bottom: -2px;
      right: -2px;
      width: 20px;
      height: 20px;
      text-align: center;
      font-size: 1.2em;
      font-family: monospace;
      text-transform: uppercase;
      padding: 0;
      border-radius: 4px;
      z-index: 10; /* 确保在图标上方 */
      background-color: rgba(0, 0, 0, 0.6);
      color: white;
      border: 1px solid white;
    }
    
    .qa-icon-dropzone {
      width: 42px;
      height: 42px;
      border: 1px dashed var(--item-border-color);
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: var(--bg-color);
    }
    .qa-icon-dropzone.has-command {
      border-style: solid;
    }
    .qa-icon-dropzone.drag-over {
      border-color: var(--accent-color);
      box-shadow: 0 0 8px var(--accent-color);
    }
    /* --- 【新增代码】为DOCK栏和自定义工具栏的拖拽提供统一的指示器样式 --- */
    .command-list .command-item,
    .command-list .separator {
      position: relative; /* 确保所有可拖拽项都有相对定位 */
    }
    
    /* 使用 ::before 伪元素在左侧绘制指示器 */
    .command-list > .drag-over-before::before {
      content: '';
      position: absolute;
      left: -6px;
      top: 2px;
      bottom: 2px;
      width: 4px;
      background-color: var(--accent-color);
      border-radius: 2px;
      z-index: 10;
    }
    
    /* 使用 ::after 伪元素在右侧绘制指示器 */
    .command-list > .drag-over-after::after {
      content: '';
      position: absolute;
      right: -6px;
      top: 2px;
      bottom: 2px;
      width: 4px;
      background-color: var(--accent-color);
      border-radius: 2px;
      z-index: 10;
    }
    /* --- 【新增结束】 --- */

    /* 【CSS新增】为“第一步”的标题和筛选框创建flex容器 */
    .form-group-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }
    .form-group-header label { 
      font-weight: bold; 
      margin-bottom: 0; /* 移除原有的下边距 */
    }
    .form-group-header input {
      width: 40%; /* 让筛选框占据一部分宽度 */
    }

    /* --- 【新增代码】模拟弹窗 (Modal) 的样式 --- */
    .modal-backdrop {
      position: fixed;
      top: 0; left: 0; right: 0; bottom: 0;
      background-color: rgba(0,0,0,0.6);
      z-index: 1500;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .modal-window {
      background-color: var(--panel-bg-color);
      border-radius: 8px;
      box-shadow: 0 5px 20px rgba(0,0,0,0.3);
      width: 75%;
      max-width: 900px;
      height: 80%;
      max-height: 750px;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }
    
    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      border-bottom: 1px solid var(--border-color);
    }
    .modal-header h3 { margin: 0; }
    .modal-header .close-btn {
      background: none;
      border: none;
      font-size: 1.8em;
      font-weight: bold;
      color: var(--text-color);
      opacity: 0.5;
    }
    .modal-header .close-btn:hover { opacity: 1; }

    /* 【CSS新增】为第一步的容器添加布局规则 */
    #step1-group {
      flex-grow: 1; /* 让它占据所有可用的垂直空间 */
      flex-shrink: 1; /* 允许它在空间不足时收缩 */
      min-height: 0;  /* 这是flex布局中防止子元素溢出的关键技巧 */
      /* 它也需要成为flex容器，来控制其内部列表的尺寸 */
      display: flex;
      flex-direction: column;
    }
    
    /* 【CSS新增】让第一步容器内部的列表可以滚动 */
    #step1-group .panel-content {
      flex-grow: 1; /* 占据父容器（#step1-group）的所有可用空间 */
      overflow-y: auto; /* 当内容超出时，自己出现滚动条 */
    }
    
    /* 【CSS新增】为第二步的容器添加布局规则 */
    #step2-group {
      flex-shrink: 0; /* 禁止此容器收缩，确保它的大小和位置永远固定 */
    }
    
    .modal-body {
      padding: 20px;
      flex-grow: 1;
      /* 【CSS核心修正1】让body成为一个flex容器，其子元素垂直排列 */
      display: flex;
      flex-direction: column;
      gap: 20px; /* 为第一步和第二步之间增加一些间距 */
      /* 【CSS核心修正2】它自身不应该滚动，所以移除overflow-y */
      overflow: hidden;
    }
    .form-group { margin-bottom: 20px; }
    .form-group label { display: block; margin-bottom: 8px; font-weight: bold; }
    .form-group select { width: 100%; padding: 8px; font-size: 1em; }
    
    .icon-picker-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 6px;
      min-height: 100px;
      max-height: 200px;
      overflow-y: auto;
      align-content: flex-start;
    }
    .icon-picker-grid .command-item {
      cursor: pointer;
      outline: 2px solid transparent;
      transition: outline-color 0.2s;
    }
    .icon-picker-grid .command-item.selected {
      outline-color: var(--accent-color);
    }
    
    .modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      padding: 10px 20px;
      border-top: 1px solid var(--border-color);
      background-color: var(--panel-header-bg-color);
    }
    /* --- 【新增结束】 --- */

    /* --- 【新增代码】弹窗内列表的选中高亮样式 --- */
    #folder-config-modal-backdrop .toolbar-item.selected {
      outline: 3px solid var(--accent-color);
      outline-offset: -1px; /* 让边框向内偏移，更美观 */
      border-color: var(--accent-color);
    }
    
    #folder-config-modal-backdrop .command-item.selected {
      outline: 3px solid var(--accent-color);
      outline-offset: 1px;
    }

    .folder-decorator {
      position: absolute;
      right: 1px;
      bottom: 1px;
      font-size: 11px;
      color: white;
      background-color: rgba(0, 0, 0, 0.4);
      border-radius: 3px;
      padding: 0 2px;
      line-height: 1.4;
      /* 【关键】让鼠标事件可以“穿透”这个标记，不会干扰拖拽 */
      pointer-events: none;
    }
    /* --- 【新增代码】用于显示Emoji图标的样式 --- */
    .command-item .emoji-icon {
      width: 32px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 28px; /* 控制Emoji的大小 */
      line-height: 32px;
      pointer-events: none;
    }
    /* --- 【新增结束】 --- */
  </style>
</head>
<body>
  <div class="app-container">
    <div class="header">
      <div class="header-left">
        <div class="header-icon">
          <img src="../ICON/ICON_Toolbar.png" alt="大" onerror="this.style.display='none'; this.parentNode.textContent='大';">
        </div>
        <div class="header-info">
          <h3 class="header-title">大锋插件管理器V3.0</h3>
          <p class="header-subtitle"><EMAIL></p>
        </div>
      </div>

      <div class="header-right">
        <!-- 汉堡菜单已删除 -->
      </div>
    </div>
    <div class="main-container">
      <div id="source-panel" class="panel" style="width: 100%;">
        <div class="panel-header">
          <div class="search-container">
            <input type="text" id="search-input" placeholder="搜索插件..." oninput="renderSourceList()">
            <span class="search-icon">⌕</span>
          </div>
        </div>
        <div id="source-list" class="panel-content"></div>
      </div>

    </div>
  </div>

  <div id="folder-config-modal-backdrop" class="modal-backdrop" style="display: none;">
    <div class="modal-window">
      <div class="modal-header">
        <h3>配置文件夹</h3>
        <button class="close-btn" onclick="closeFolderConfigDialog()">&times;</button>
      </div>
      <div class="modal-body">
      </div>
      <div class="modal-footer">
        <button onclick="closeFolderConfigDialog()">取消</button>
        <button class="primary-btn" onclick="saveFolderConfig()">确定</button>
      </div>
    </div>
  </div>

  <script>
    let allSourceData = [], allCustomToolbarData = [], allRadialData = {};
    let allDockBarData = [], allDockAssignments = {};
    let dockSlotSettings = {};
    let pluginVersion = '';
    let draggedId = null, draggedFromCustom = false, draggedFromSlotInfo = null;
    let pendingDiskToExpandId = null; // 【新增】用于存储待展开的圆盘ID
    let draggedFromDockInfo = null;
    let draggedFromToolbarInfo = null; // 【新增】
    let sketchupVersion = 0;
    let currentDockIdForFolder = null;
    let currentFolderConfigSource = { // 全局变量，存储当前配置状态
      dockId: null,
      targetItemId: null,
      sourceToolbarName: null,
      coverCmdId: null
    };

    const GENERIC_ICONS = [
      { type: 'emoji', unique_id: 'emoji_folder', tooltip: '文件夹', emoji: '📁' },
      { type: 'emoji', unique_id: 'emoji_star', tooltip: '星标', emoji: '⭐' },
      { type: 'emoji', unique_id: 'emoji_heart', tooltip: '喜爱', emoji: '❤️' },
      { type: 'emoji', unique_id: 'emoji_tool', tooltip: '工具', emoji: '🛠️' },
      { type: 'emoji', unique_id: 'emoji_play', tooltip: '媒体', emoji: '▶️' },
      { type: 'emoji', unique_id: 'emoji_check', tooltip: '完成', emoji: '✅' }
    ];
    
    function renderAllData(payload) {
      // 1. 从 payload 中解析出所有数据
      sketchupVersion = payload.sketchupVersion;
      pluginVersion = payload.pluginVersion;
      allSourceData = payload.sourceData;
      allCustomToolbarData = payload.customToolbarData;
      allRadialData = payload.radialData;
      allDockBarData = payload.dockBarData;
      allDockAssignments = payload.dockAssignments;
      dockSlotSettings = payload.dockSlotSettings || {}; // 【新增】接收插槽设置
      window.pluginGroups = payload.pluginGroups || {}; // 【新增】接收分组配置
      window.displaySettings = payload.displaySettings || { show_separators: true }; // 【新增】接收显示设置
      console.log('Received display settings:', window.displaySettings);

      const diskToFocusId = payload.diskToFocus;

      if (diskToFocusId) {
          setPendingDiskToExpand(diskToFocusId);
      }

      // 3. 只渲染源命令列表
      renderSourceList();

      // 4. 更新分隔符切换按钮文本
      updateSeparatorToggleText();
    }

        // 【新增】根据后端数据，渲染拨动钮的初始状态
    function renderDockSlotSettings() {
      ['top', 'right', 'bottom'].forEach(slotName => {
        const switchEl = document.getElementById(`auto-hide-switch-${slotName}`);
        if (switchEl) {
          // --- 【核心修正】 ---
          // a. 从读取旧的 'auto_hide' 改为读取新的 'show_auto_hide_button'
          // b. 同时将默认值从 true 改为 false，因为按钮默认不显示
          const shouldBeVisible = (dockSlotSettings[slotName] && dockSlotSettings[slotName].show_auto_hide_button !== undefined) 
                                ? dockSlotSettings[slotName].show_auto_hide_button 
                                : false;
          
          switchEl.checked = shouldBeVisible;
          // --- 【修正结束】 ---
        }
      });
    }

    // 【修改】处理拨动钮点击事件的函数
    function handleAutoHideToggle(slotName, checkboxElement) {
      const isVisible = checkboxElement.checked;
      // 调用新的Ruby回调，只控制按钮的“显示/隐藏”
      sketchup.set_dock_control_button_visibility(slotName, isVisible);
    }

    // 【JS新增函数】用于过滤弹窗内的工具栏列表
    function filterFolderSourceList() {
      const searchTerm = document.getElementById('folder-source-filter').value.toLowerCase();
      // 注意：这里我们从全局变量获取数据进行过滤
      const allToolbars = allSourceData.concat(allCustomToolbarData); 
      const filteredToolbars = allToolbars.filter(tb => tb.name.toLowerCase().includes(searchTerm));
    
      // 重新渲染列表，但只传递过滤后的数据，并告知函数这是在筛选
      renderFolderSourceList(filteredToolbars, true);
    
      // 如果之前有选中的，需要重新高亮
      if (currentFolderConfigSource.sourceToolbarName) {
        const listContainer = document.getElementById('folder-source-list-container');
        const selectedItem = Array.from(listContainer.querySelectorAll('.toolbar-item')).find(el => 
          el.querySelector('.toolbar-name-header').textContent === currentFolderConfigSource.sourceToolbarName
        );
        if (selectedItem) {
          selectedItem.classList.add('selected');
        }
      }
    }

    // 1. 打开文件夹配置弹窗
    function openFolderConfigDialog(dockId, targetItemId) {

        currentFolderConfigSource.dockId = dockId;
        currentFolderConfigSource.targetItemId = targetItemId;
    
        const modal = document.getElementById('folder-config-modal-backdrop');
        modal.style.display = 'flex';
    
        // 【核心修正】直接合并前端已有的数据，不再请求后端
        try {
            const allToolbars = allSourceData.concat(allCustomToolbarData);
            allToolbars.sort((a, b) => a.name.localeCompare(b.name));
            renderFolderSourceList(allToolbars);
        } catch (e) {
            console.error("渲染可用工具栏列表失败:", e);
        }
    }

    // 【新增】这个函数将由Ruby后端主动调用
    function receiveAvailableToolbars(toolbars) {
      try {
        // 接收到数据后，调用我们之前的渲染函数
        renderFolderSourceList(toolbars);
      } catch (e) {
        console.error("渲染可用工具栏列表失败:", e);
        const body = document.querySelector('#folder-config-modal-backdrop .modal-body');
        body.innerHTML = '<p style="color: red;">错误：渲染工具栏列表失败，请查看控制台日志。</p>';
      }
    }
    
    // 2. 渲染弹窗内的源工具栏列表 (核心渲染函数)
    // 【JS修改】修改此函数，为第一步和第二步的容器添加ID
    function renderFolderSourceList(sourceData, isFiltering = false) {
      const body = document.querySelector('#folder-config-modal-backdrop .modal-body');
      
      if (!isFiltering) {
        body.innerHTML = '';
    
        const sourceSelectGroup = document.createElement('div');
        sourceSelectGroup.className = 'form-group';
        sourceSelectGroup.id = 'step1-group'; // 【JS核心修正1】为第一步容器添加ID
    
        const groupHeader = document.createElement('div');
        groupHeader.className = 'form-group-header';
        groupHeader.innerHTML = `<label>第一步: 选择一个工具栏或其中的图标</label>
                                 <input type="text" id="folder-source-filter" placeholder="筛选工具栏..." oninput="filterFolderSourceList()">`;
        
        const sourceListContainer = document.createElement('div');
        sourceListContainer.id = 'folder-source-list-container';
        sourceListContainer.className = 'panel-content';
        // 不再需要在这里设置max-height，由CSS的flex布局全面接管
        // sourceListContainer.style.maxHeight = '35vh'; 
    
        sourceSelectGroup.appendChild(groupHeader);
        sourceSelectGroup.appendChild(sourceListContainer);
    
        const iconPickerGroup = document.createElement('div');
        iconPickerGroup.className = 'form-group';
        iconPickerGroup.id = 'step2-group'; // 【JS核心修正2】为第二步容器添加ID
    
        iconPickerGroup.innerHTML = '<label>第二步: 确认封面图标</label>';
        const iconPickerContainer = document.createElement('div');
        iconPickerContainer.id = 'folder-icon-picker';
        iconPickerContainer.className = 'icon-picker-grid';
        iconPickerContainer.innerHTML = '<p>请先在上方选择</p>';
        iconPickerGroup.appendChild(iconPickerContainer);
    
        body.appendChild(sourceSelectGroup);
        body.appendChild(iconPickerGroup);
      }
    
      const listContainer = document.getElementById('folder-source-list-container');
      listContainer.innerHTML = '';
    
      (sourceData || []).forEach(toolbar => {
          const toolbarDiv = document.createElement('div');
          toolbarDiv.className = 'toolbar-item';
          const headerDiv = document.createElement('div');
          headerDiv.className = 'toolbar-name-header';
          headerDiv.textContent = toolbar.name;
          
          toolbarDiv.onclick = () => handleToolbarHeaderClick(toolbar.name, toolbarDiv);
          
          const list = document.createElement('div');
          list.className = 'command-list';
          
          (toolbar.commands || []).forEach(cmd => {
              if (cmd.type !== 'command') return;
              const cmdEl = createCommandElement(cmd, false);
              cmdEl.onclick = (e) => {
                e.stopPropagation();
                handleSourceIconClick(cmd, toolbar.name, cmdEl);
              };
              list.appendChild(cmdEl);
          });
    
          toolbarDiv.appendChild(headerDiv); 
          toolbarDiv.appendChild(list); 
          listContainer.appendChild(toolbarDiv);
      });
    }
    
    // 3. 处理点击工具栏标题的事件
    function handleToolbarHeaderClick(toolbarName, toolbarElement) {
        currentFolderConfigSource.sourceToolbarName = toolbarName;
        currentFolderConfigSource.coverCmdId = 'emoji_folder'; // 点击标题时，重置为默认封面
    
        const modal = document.getElementById('folder-config-modal-backdrop');
        // 先移除所有条目的选中状态
        modal.querySelectorAll('.toolbar-item.selected').forEach(el => el.classList.remove('selected'));
        
        // 【核心修正】在选中新工具栏之前，清除第一步列表中任何可能被选中的图标的高亮
        const sourceListContainer = document.getElementById('folder-source-list-container');
        if (sourceListContainer) {
            const previouslySelectedIcon = sourceListContainer.querySelector('.command-item.selected');
            if (previouslySelectedIcon) {
                previouslySelectedIcon.classList.remove('selected');
            }
        }
        // --- 修正结束 ---
    
        // 再为当前点击的条目添加选中状态
        toolbarElement.classList.add('selected');
        
        populateIconPicker(toolbarName, 'emoji_folder');
    }
    
    // 4. 处理点击源列表中图标的事件
    function handleSourceIconClick(cmd, toolbarName, cmdElement) {
        currentFolderConfigSource.sourceToolbarName = toolbarName;
        currentFolderConfigSource.coverCmdId = cmd.unique_id;
    
        const modal = document.getElementById('folder-config-modal-backdrop');
        // 先移除所有条目的选中状态
        modal.querySelectorAll('.toolbar-item.selected').forEach(el => el.classList.remove('selected'));
        
        // 【核心修正】先清除旧的图标高亮，再添加新的
        modal.querySelectorAll('.command-item.selected').forEach(el => el.classList.remove('selected'));
        cmdElement.classList.add('selected'); // 高亮当前点击的图标
        // --- 修正结束 ---
    
        // 找到图标所在的整个工具栏条目，并高亮它
        cmdElement.closest('.toolbar-item').classList.add('selected');
    
        // 调用populateIconPicker，它内部会调用selectCoverIcon来高亮第二步的图标
        populateIconPicker(toolbarName, cmd.unique_id);
    }
    
    // 5. 填充封面图标选择区的函数
    function populateIconPicker(toolbarName, defaultSelectedId) {
        const picker = document.getElementById('folder-icon-picker');
        picker.innerHTML = '';
    
        // 当点击第二步中的通用图标时，调用selectCoverIcon并明确告知非来自第一步
        GENERIC_ICONS.forEach(icon => {
            const iconEl = createCommandElement(icon, false);
            iconEl.onclick = () => selectCoverIcon(icon.unique_id, iconEl, false); // 传递 false
            picker.appendChild(iconEl);
        });
    
        const sourceList = allSourceData.concat(allCustomToolbarData);
        const sourceToolbar = sourceList.find(tb => tb.name === toolbarName);
        if (sourceToolbar) {
            (sourceToolbar.commands || []).forEach(cmd => {
                if (cmd.type !== 'command') return;
                const iconEl = createCommandElement(cmd, false);
                // 当点击第二步中的源图标时，也明确告知非来自第一步
                iconEl.onclick = () => selectCoverIcon(cmd.unique_id, iconEl, false); // 传递 false
                picker.appendChild(iconEl);
            });
        }
    
        // 【核心修正】当由第一步操作触发此函数时，调用selectCoverIcon并告知操作源
        // 这个调用是用来高亮第二步的默认选中项的
        selectCoverIcon(defaultSelectedId, null, true); // 传递 true
    }
    
    // 6. 处理封面图标选择的函数
    function selectCoverIcon(cmdId, iconElement, initiatedFromStep1 = false) {
        currentFolderConfigSource.coverCmdId = cmdId;
        const picker = document.getElementById('folder-icon-picker');
        
        // 清除第二步(picker)中的所有高亮
        picker.querySelectorAll('.command-item.selected').forEach(el => el.classList.remove('selected'));
        
        // 【核心修正】只有当操作不是从第一步发起时，才去清除第一步的高亮
        if (!initiatedFromStep1) {
            const sourceListContainer = document.getElementById('folder-source-list-container');
            if (sourceListContainer) {
                const previouslySelectedIcon = sourceListContainer.querySelector('.command-item.selected');
                if (previouslySelectedIcon) {
                    previouslySelectedIcon.classList.remove('selected');
                }
            }
        }
        // --- 修正结束 ---
    
        // 高亮第二步中新选中的图标
        let targetEl = iconElement;
        if (!targetEl) {
            targetEl = Array.from(picker.querySelectorAll('.command-item')).find(el => el.dataset.id === cmdId);
        }
        if (targetEl) {
            targetEl.classList.add('selected');
        }
    }

    // 7. 关闭弹窗和保存的占位函数（这些保持不变）
    function closeFolderConfigDialog() {
        const modal = document.getElementById('folder-config-modal-backdrop');
        modal.style.display = 'none';
    
        // 【核心修正】在隐藏窗口的同时，重置全局状态变量
        currentFolderConfigSource = { 
          dockId: null,
          targetItemId: null,
          sourceToolbarName: null,
          coverCmdId: null
        };
    }
        
    function saveFolderConfig() {
        const { dockId, sourceToolbarName, coverCmdId } = currentFolderConfigSource;
    
        // 从全局数据 allDockBarData 中，根据 dockId 找到我们当前正在操作的 DOCK 栏
        const targetDock = allDockBarData.find(db => db.id === dockId);
    
        // 检查这个 DOCK 栏的 commands 数组，查找是否已存在同源文件夹
        if (targetDock && targetDock.commands.some(cmd => cmd.type === 'folder' && cmd.source_toolbar_name === sourceToolbarName)) {
            showStatusFeedback(`错误：当前DOCK栏中已存在引用 "${sourceToolbarName}" 的文件夹。`, true);
            
            // 【核心修正】删除下面这行多余的代码，让弹窗在提示错误后保持打开
            // closeFolderConfigDialog(); 
            
            return; // 只返回，不关闭
        }
    
        // 如果检查通过，才继续执行后面的所有逻辑
        if (!sourceToolbarName) {
            showStatusFeedback("请先在“第一步”中选择一个源工具栏。", true);
            return;
        }
        if (!coverCmdId) {
            showStatusFeedback("请在“第二步”中确认一个封面图标。", true);
            return;
        }
    
        const coverCmdData = findCommandDataById(coverCmdId) || GENERIC_ICONS.find(ic => ic.unique_id === coverCmdId);
    
        if (!coverCmdData) {
            showStatusFeedback("错误：找不到所选封面图标的数据。", true);
            console.error("无法找到ID为 " + coverCmdId + " 的封面图标数据。");
            return;
        }
    
        // 后面的 Emoji 转图片并调用后端的逻辑保持不变
        if (coverCmdData.type === 'emoji') {
            const canvas = document.createElement('canvas');
            canvas.width = 96;
            canvas.height = 96;
            const ctx = canvas.getContext('2d');
            ctx.font = '72px Segoe UI Emoji';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(coverCmdData.emoji, canvas.width / 2, canvas.height / 2);
            const base64Png = canvas.toDataURL('image/png').split(',')[1];
            sketchup.add_folder_to_dock_bar(dockId, sourceToolbarName, coverCmdId, base64Png, false);
            closeFolderConfigDialog();
        } else {
            const img = new Image();
            img.onload = () => {
                const canvas = document.createElement('canvas');
                canvas.width = 96;
                canvas.height = 96;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, 96, 96);
                const base64Png = canvas.toDataURL('image/png').split(',')[1];
                sketchup.add_folder_to_dock_bar(dockId, sourceToolbarName, coverCmdId, base64Png, false);
                closeFolderConfigDialog();
            };
            img.onerror = () => { 
                showStatusFeedback("错误：加载封面图标失败。", true); 
            };
            img.src = coverCmdData.icon_data_uri;
        }
    }

    // 汉堡菜单相关函数已删除

    
    // 修改window的点击事件，关闭右键菜单
    window.addEventListener('click', () => {
      hideContextMenu(); // 关闭右键菜单
    });



    // 主题切换和分隔符切换功能已移除，默认使用白天模式和隐藏分隔符

    function renderDockBars(dockBarData, assignmentData) {
      // --- 【核心修改】在函数开头增加版本检查 ---
      const container = document.getElementById('persistent-toolbars-content');
    
      if (sketchupVersion < 23) {
        // 如果版本低于2023，清空内容并显示提示信息
        container.innerHTML = `
          <div style="display: flex; justify-content: center; align-items: center; height: 100%; color: var(--text-color); opacity: 0.7;">
            <h2>该功能需要 SketchUp 2023 以上版本支持</h2>
          </div>
        `;
        // 提前结束函数，不执行后续的渲染逻辑
        return;
      }
      // --- 修改结束 ---
    
    
      // 如果版本检查通过，则执行原有的渲染逻辑
      const listContainer = document.getElementById('dock-bars-list-container');
      listContainer.innerHTML = ''; 
      
      const assignedIds = new Set([].concat((assignmentData.top || []), (assignmentData.bottom || []), (assignmentData.right || [])));
      const dockBarMap = new Map(dockBarData.map(db => [db.id, db]));
    
      // 步骤 1: 渲染三个停靠槽
      ['top', 'bottom', 'right'].forEach(slotName => {
        const contentContainer = document.getElementById(`dock-slot-${slotName}`);
        const headerContainer = contentContainer.previousElementSibling; 
        contentContainer.innerHTML = ''; 
    
        const assignedDocks = (assignmentData[slotName] || []);
        
        headerContainer.classList.toggle('has-content', assignedDocks.length > 0);
    
        assignedDocks.forEach(dockId => {
          const dockBar = dockBarMap.get(dockId);
          if (dockBar) {
            const dockBarDiv = createDockBarElement(dockBar);
            contentContainer.appendChild(dockBarDiv);
          }
        });
      });
      
      // 步骤 2: 渲染下方列表中所有“未被分配”的DOCK栏
      dockBarData.forEach(dockBar => {
        if (!assignedIds.has(dockBar.id)) {
          const dockBarDiv = createDockBarElement(dockBar);
          listContainer.appendChild(dockBarDiv);
        }
      });
    }

    // 【新增】创建DOCK栏HTML元素的函数
    function createDockBarElement(dockBar) {
        const dockBarDiv = document.createElement('div');
        dockBarDiv.className = 'toolbar-item';
        dockBarDiv.dataset.id = dockBar.id;
        dockBarDiv.draggable = true;
    
        dockBarDiv.addEventListener('dragstart', (e) => {
          e.stopPropagation();
          e.dataTransfer.setData('one-toolbar/dock-bar-item', dockBar.id);
          e.dataTransfer.effectAllowed = 'move';
          setTimeout(() => dockBarDiv.classList.add('is-dragging'), 0);
        });
        dockBarDiv.addEventListener('dragend', (e) => {
            dockBarDiv.classList.remove('is-dragging');
            document.querySelectorAll('.dock-slot').forEach(el => el.classList.remove('drag-over-inside'));
        });
        
        dockBarDiv.addEventListener('contextmenu', (event) => {
            if (event.target.closest('.command-item, .separator')) return;
            event.preventDefault();
            event.stopPropagation();
            const menuItems = [
                { label: '创建文件夹', action: () => openFolderConfigDialog(dockBar.id, null) }, 
                { label: '添加分隔符', action: () => sketchup.add_separator_to_dock_bar(dockBar.id) },
                { type: 'separator' },
                { label: '重命名DOCK栏', action: () => sketchup.prompt_and_rename_dock_bar(dockBar.id, dockBar.name) },
                { label: '删除DOCK栏', action: () => sketchup.confirm_and_delete_dock_bar(dockBar.id, dockBar.name) }
            ];
            showContextMenu(event, menuItems);
        });
    
        const headerDiv = document.createElement('div');
        headerDiv.className = 'toolbar-name-header';
        headerDiv.innerHTML = `<span>${dockBar.name}</span>`;
    
        const list = document.createElement('div');
        list.className = 'command-list';
        list.dataset.dockId = dockBar.id;
        addDropListenersToDockBar(list);
    
        (dockBar.commands || []).forEach(item => {
          if (item.type === 'command' || item.type === 'folder') {
            const itemEl = createCommandElement(item, false, null, null);
            
            list.appendChild(itemEl);
    
            itemEl.addEventListener('contextmenu', (event) => {
                event.preventDefault();
                event.stopPropagation();
    
                let menuItems = [];
                if (item.type === 'folder') {
                  // --- 这是核心修改点 ---
                  menuItems = [
                      // 【新增】重绘图标的菜单项
                      { 
                        label: '重绘图标', 
                        // --- 这是核心修改点 ---
                        // 不再使用JS的confirm，而是直接调用Ruby的新回调
                        // 并将需要的参数（源工具栏名称和文件夹提示）传递过去
                        action: () => sketchup.confirm_and_redraw_folder_icons(item.source_toolbar_name, item.tooltip)
                      },
                      { type: 'separator' }, // 加一个分隔线
                      { label: '在此处添加分隔符', action: () => sketchup.add_separator_after_item(dockBar.id, item.id) },
                      { label: '删除此文件夹', action: () => sketchup.delete_item_from_dock_bar(dockBar.id, item.id) }
                  ];
                } else {
                  menuItems = [
                      { label: '在此处添加分隔符', action: () => sketchup.add_separator_after_item(dockBar.id, item.unique_id) },
                      { label: '删除此图标', action: () => sketchup.delete_command_from_dock_bar(dockBar.id, item.unique_id) }
                  ];
                }
                showContextMenu(event, menuItems);
            });
    
          } else if (item.type === 'separator') {
            const sepEl = document.createElement('div');
            sepEl.className = 'separator';
            sepEl.dataset.id = item.id;
            sepEl.draggable = true;
            sepEl.addEventListener('dragstart', (e) => { e.stopPropagation(); draggedFromDockInfo = { dockId: dockBar.id, itemId: item.id }; e.dataTransfer.setData('one-toolbar/separator-item', item.id); e.dataTransfer.effectAllowed = 'move'; setTimeout(() => sepEl.classList.add('dragging'), 0); });
            sepEl.addEventListener('dragend', (e) => { draggedFromDockInfo = null; });
            
            sepEl.addEventListener('contextmenu', (event) => {
                event.preventDefault();
                event.stopPropagation();
                const menuItems = [
                    { label: '删除分隔符', action: () => sketchup.delete_separator_from_dock(dockBar.id, item.id) }
                ];
                showContextMenu(event, menuItems);
            });
    
            list.appendChild(sepEl);
          }
        });
        
        dockBarDiv.appendChild(headerDiv);
        dockBarDiv.appendChild(list); 
        return dockBarDiv;
    }

    // 【新增】为停靠槽绑定拖放事件的函数
    function addDropListenersToDockSlot(slotElement, slotName) {
      let lastDragOverElement = null; // 用于追踪悬停元素
    
      slotElement.addEventListener('dragover', (e) => {
        if (e.dataTransfer.types.includes('one-toolbar/dock-bar-item')) {
          e.preventDefault();
          
          const targetItem = e.target.closest('.toolbar-item[data-id]');
          
          // 清理旧的高亮
          if (lastDragOverElement && lastDragOverElement !== targetItem) {
            lastDragOverElement.classList.remove('drag-over-top', 'drag-over-bottom');
          }
          slotElement.classList.remove('drag-over-inside');
    
          if (targetItem) { // 如果悬停在已有的DOCK栏上，显示排序指示器
            lastDragOverElement = targetItem;
            const rect = targetItem.getBoundingClientRect();
            const isOverTopHalf = e.clientY < rect.top + rect.height / 2;
            targetItem.classList.toggle('drag-over-top', isOverTopHalf);
            targetItem.classList.toggle('drag-over-bottom', !isOverTopHalf);
          } else { // 如果悬停在停靠槽的空白处，高亮整个槽
            lastDragOverElement = null;
            slotElement.classList.add('drag-over-inside');
          }
        }
      });
    
      slotElement.addEventListener('dragleave', (e) => {
        slotElement.classList.remove('drag-over-inside');
        if (lastDragOverElement) {
          lastDragOverElement.classList.remove('drag-over-top', 'drag-over-bottom');
        }
      });
    
      slotElement.addEventListener('drop', (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        // 清理所有高亮
        slotElement.classList.remove('drag-over-inside');
        if (lastDragOverElement) {
          lastDragOverElement.classList.remove('drag-over-top', 'drag-over-bottom');
        }
        
        const dockId = e.dataTransfer.getData('one-toolbar/dock-bar-item');
        if (!dockId) return;
    
        let targetId = null;
        let isAfter = false;
        const targetItem = e.target.closest('.toolbar-item[data-id]');
    
        if (targetItem) {
          targetId = targetItem.dataset.id;
          const rect = targetItem.getBoundingClientRect();
          isAfter = e.clientY >= rect.top + rect.height / 2;
        }
        
        sketchup.assign_dock_bar_to_slot(dockId, slotName, targetId, isAfter);
      });
    }

    function addDropListenerToDockBarList(listElement) {
      listElement.addEventListener('dragover', (e) => {
        // 只有当拖动的是一个DOCK栏条目时才响应
        if (e.dataTransfer.types.includes('one-toolbar/dock-bar-item')) {
          e.preventDefault();
          listElement.classList.add('drag-over-inside'); // 复用高亮样式
        }
      });
    
      listElement.addEventListener('dragleave', (e) => {
        listElement.classList.remove('drag-over-inside');
      });
    
      listElement.addEventListener('drop', (e) => {
        e.preventDefault();
        listElement.classList.remove('drag-over-inside');
        const dockId = e.dataTransfer.getData('one-toolbar/dock-bar-item');
        if (!dockId) return;
    
        // 调用新的后端方法来“取消分配”
        sketchup.unassign_dock_bar(dockId);
      });
    }

    // 定义插件分组规则（从后端配置获取）
    function getPluginGroup(toolbarName) {
      // 使用全局变量中的分组配置，如果没有则使用默认配置
      const groupRules = window.pluginGroups || {};

      for (const [groupName, toolbars] of Object.entries(groupRules)) {
        if (toolbars.some(name => toolbarName.includes(name) || name.includes(toolbarName))) {
          return groupName;
        }
      }

      return '默认分组';
    }

    function renderSourceList() {
      const container = document.getElementById('source-list');
      container.innerHTML = '';
      const searchTerm = document.getElementById('search-input').value.toLowerCase();



      // 按分组整理工具栏
      const groupedToolbars = {};

      (allSourceData || []).forEach(toolbar => {
        // 首先，过滤出包含搜索词的命令。不过滤分隔符。
        const filteredCommands = toolbar.commands.filter(cmd =>
            cmd.type === 'command' && (cmd.tooltip.toLowerCase().includes(searchTerm) || toolbar.name.toLowerCase().includes(searchTerm))
        );

        // 决定最终要渲染哪些条目。如果无搜索，则渲染全部。
        // 如果有搜索，则仅当工具栏名称匹配或其下有匹配的命令时，才渲染整个工具栏（包括分隔符）。
        const toolbarNameMatches = toolbar.name.toLowerCase().includes(searchTerm);
        if (searchTerm && !toolbarNameMatches && filteredCommands.length === 0) {
            return; // 如果搜索不匹配任何东西，则跳过此工具栏
        }

        const itemsToRender = searchTerm ? toolbar.commands.filter(cmd => cmd.type === 'separator' || filteredCommands.includes(cmd)) : toolbar.commands;

        // 如果没有可渲染的内容，也跳过
        if (itemsToRender.length === 0) {
            return;
        }

        // 获取分组名称
        const groupName = getPluginGroup(toolbar.name);
        if (!groupedToolbars[groupName]) {
          groupedToolbars[groupName] = [];
        }
        groupedToolbars[groupName].push({
          toolbar: toolbar,
          itemsToRender: itemsToRender
        });
      });

      // 按分组渲染
      // 只保留一个默认分组，其他都是自定义分组
      const defaultGroups = ['默认分组'];
      const allGroups = Object.keys(window.pluginGroups || {});
      const customGroups = allGroups.filter(group => !defaultGroups.includes(group));
      const groupOrder = [...defaultGroups, ...customGroups];



      groupOrder.forEach(groupName => {
        // 检查是否有工具栏数据
        const hasToolbars = groupedToolbars[groupName] && groupedToolbars[groupName].length > 0;
        const isSearching = searchTerm.length > 0;



        // 在搜索模式下，只显示有工具栏的分组
        // 在非搜索模式下，显示所有分组（包括空分组）
        if (isSearching && !hasToolbars) {
          console.log(`Skipping group ${groupName} due to search filter`);
          return;
        }

        // 计算展开状态和指示器
        // 搜索时展开，空分组也默认展开以便用户看到
        const shouldExpand = isSearching || !hasToolbars;
        const initialIndicator = shouldExpand ? '▼' : '▶';

        // 创建分组标题
        const groupHeader = document.createElement('div');
        groupHeader.className = 'group-header';
        groupHeader.draggable = true;
        groupHeader.dataset.groupName = groupName;
        groupHeader.innerHTML = `
          <span class="group-expand-indicator">${initialIndicator}</span>
          <span class="group-title">${groupName}</span>
          <span class="drag-handle">⋮⋮</span>
        `;

        // 创建分组容器
        const groupContainer = document.createElement('div');
        groupContainer.className = 'group-container';
        // 搜索时默认展开，空分组也默认展开以便用户看到，否则默认折叠
        groupContainer.style.display = shouldExpand ? 'block' : 'none';

        // 添加分组拖拽事件
        groupHeader.addEventListener('dragstart', (e) => {
          // 整个标题区域都可以拖拽（零风险修改）
          e.dataTransfer.setData('one-toolbar/group-item', groupName);
          e.dataTransfer.effectAllowed = 'move';
          setTimeout(() => groupHeader.classList.add('dragging'), 0);
        });

        groupHeader.addEventListener('dragend', () => {
          groupHeader.classList.remove('dragging');
        });

        // 添加分组展开/折叠功能
        groupHeader.addEventListener('click', (e) => {
          // 整个标题区域都可以点击展开/折叠（零风险修改）
          const isExpanded = groupContainer.style.display !== 'none';
          groupContainer.style.display = isExpanded ? 'none' : 'block';
          const indicator = groupHeader.querySelector('.group-expand-indicator');
          indicator.textContent = isExpanded ? '▶' : '▼';
        });

        // 添加分组右键菜单功能
        groupHeader.addEventListener('contextmenu', (e) => {
          e.preventDefault();
          e.stopPropagation();
          const menuItems = [
            { label: '收展所有组', action: () => toggleAllGroups() },
            { type: 'separator' },
            { label: '添加分组', action: () => promptAndCreateGroup() }
          ];

          // 只有非默认分组才显示重命名和删除选项
          if (groupName !== '默认分组') {
            menuItems.push(
              { label: '重命名', action: () => promptAndRenameGroup(groupName) },
              { label: '删除分组', action: () => confirmAndDeleteGroup(groupName) }
            );
          }

          showContextMenu(e, menuItems);
        });


        container.appendChild(groupHeader);
        container.appendChild(groupContainer);

        // 渲染该分组下的工具栏（如果有的话）
        if (hasToolbars) {
          groupedToolbars[groupName].forEach(({toolbar, itemsToRender}) => {
          const list = document.createElement('div');
          list.className = 'command-list';
          // 如果有搜索词，则展开显示；否则默认隐藏
          const toolbarShouldExpand = searchTerm.length > 0;
          list.style.display = toolbarShouldExpand ? 'flex' : 'none';

        // ==================================================================================
        // ▼▼▼ 这是最核心的修改：我们在这里检查每个条目的类型！ ▼▼▼
        // ==================================================================================
        itemsToRender.forEach(item => {
          if (item.type === 'command') {
            // 如果是命令，创建命令元素并添加点击事件
            const cmdEl = createCommandElement(item, false);

            // 添加点击事件监听器来执行命令
            cmdEl.addEventListener('click', (e) => {
              e.preventDefault();
              e.stopPropagation();
              // 调用SketchUp执行命令
              if (typeof sketchup !== 'undefined' && sketchup.execute_command_by_id) {
                sketchup.execute_command_by_id(item.unique_id);
              } else {
                console.log('执行命令:', item.unique_id, item.tooltip);
              }
            });

            // 修改鼠标样式，表示可点击
            cmdEl.style.cursor = 'pointer';

            list.appendChild(cmdEl);
          } else if (item.type === 'separator') {
            // 检查是否应该显示分隔符
            const shouldShowSeparators = window.displaySettings && window.displaySettings.show_separators !== false;
            console.log('Separator check:', { displaySettings: window.displaySettings, shouldShow: shouldShowSeparators });
            if (shouldShowSeparators) {
              // 如果是分隔符，创建一个专门的HTML元素来代表它
              const sepEl = document.createElement('div');
              sepEl.className = 'separator';
              // 源列表里的分隔符是不可交互的，所以我们让它看起来更“哑”一些
              sepEl.style.cursor = 'default';
              sepEl.style.opacity = '0.5';

              list.appendChild(sepEl);
            }
          }
        });
        // ======================== ▲▲▲ 核心修改结束 ▲▲▲ ========================

          // 只有当列表里真的有内容时，才创建并添加整个工具栏的DOM结构
          if (list.hasChildNodes()) {
            const toolbarDiv = document.createElement('div');
            toolbarDiv.className = 'toolbar-item';

            const headerDiv = document.createElement('div');
            headerDiv.className = 'toolbar-name-header collapsible-header';
            headerDiv.draggable = true;
            headerDiv.dataset.toolbarName = toolbar.name;
            headerDiv.dataset.groupName = groupName;
            const indicatorSymbol = toolbarShouldExpand ? '▼' : '▶';
            // 确定工具栏状态（从后端数据获取实际可见性状态）
            const isVisible = toolbar.visible !== false;
            const toolbarStatus = isVisible ? '●' : '○';
            const statusColor = isVisible ? '#28a745' : '#6c757d';
            const statusTitle = isVisible ? '工具栏已显示' : '工具栏已隐藏';

            headerDiv.innerHTML = `
              <span class="expand-indicator">${indicatorSymbol}</span>
              <span class="toolbar-name">${toolbar.name}</span>
              <span class="toolbar-status" style="color: ${statusColor}" title="${statusTitle}">${toolbarStatus}</span>
              <span class="drag-handle">⋮⋮</span>
            `;

            // 如果搜索时展开了，添加expanded类
            if (toolbarShouldExpand) {
              toolbarDiv.classList.add('expanded');
            }

            // 添加工具栏拖拽事件
            headerDiv.addEventListener('dragstart', (e) => {
              // 整个标题区域都可以拖拽（零风险修改）
              e.dataTransfer.setData('one-toolbar/toolbar-item', JSON.stringify({
                toolbarName: toolbar.name,
                sourceGroup: groupName
              }));
              e.dataTransfer.effectAllowed = 'move';
              setTimeout(() => headerDiv.classList.add('dragging'), 0);
            });

            headerDiv.addEventListener('dragend', () => {
              headerDiv.classList.remove('dragging');
            });

            // 添加点击事件来切换展开/折叠
            headerDiv.addEventListener('click', (e) => {
              // 整个标题区域都可以点击展开/折叠（零风险修改）
              const isExpanded = list.style.display !== 'none';
              list.style.display = isExpanded ? 'none' : 'flex';
              const indicator = headerDiv.querySelector('.expand-indicator');
              indicator.textContent = isExpanded ? '▶' : '▼';
              toolbarDiv.classList.toggle('expanded', !isExpanded);
            });

            // 添加工具栏右键菜单功能
            headerDiv.addEventListener('contextmenu', (e) => {
              e.preventDefault();
              e.stopPropagation();
              const menuItems = [
                { label: '显隐工具条', action: () => toggleToolbarVisibility(toolbar.name) },
                { label: '移动到分组', action: () => showMoveToGroupDialog(toolbar.name) },
                { type: 'separator' },
                { label: '卸载插件', action: () => confirmUninstallPlugin(toolbar.name) }
              ];
              showContextMenu(e, menuItems);
            });

            toolbarDiv.appendChild(headerDiv);
            toolbarDiv.appendChild(list);
            groupContainer.appendChild(toolbarDiv); // 添加到分组容器而不是主容器
          }
          });
        } else {
          // 如果分组为空，显示一个提示
          const emptyMessage = document.createElement('div');
          emptyMessage.className = 'empty-group-message';
          emptyMessage.textContent = '此分组暂无插件';
          groupContainer.appendChild(emptyMessage);
        }
      });

      // 如果容器仍然为空，显示一个提示
      if (container.children.length === 0) {
        const noDataMessage = document.createElement('div');
        noDataMessage.className = 'no-data-message';
        noDataMessage.innerHTML = `
          <p>暂无插件数据</p>
          <p>请确保SketchUp中已安装插件</p>
        `;
        container.appendChild(noDataMessage);
      }

      // 添加拖拽排序功能
      addDragDropHandlersToSourceList(container);
    }





    function createSliderControl(labelText, initialValue, min, max, callback) {
      const container = document.createElement('div');
      container.className = 'slider-control';

      const label = document.createElement('label');
      label.textContent = labelText;

      const valueSpan = document.createElement('span');
      valueSpan.textContent = initialValue;

      const slider = document.createElement('input');
      slider.type = 'range';
      slider.min = min;
      slider.max = max;
      slider.value = initialValue;

      slider.onmousedown = (event) => {
        event.stopPropagation();
      };

      slider.oninput = () => {
        valueSpan.textContent = slider.value;
      };
      
      slider.onchange = () => {
        callback(parseInt(slider.value, 10));
      };

      label.appendChild(valueSpan);
      container.appendChild(label);
      container.appendChild(slider);
      return container;
    }

    function createDiskContainerElement(disk, allGroups) {
      const container = document.createElement('div');
      container.className = 'disk-container';
      container.id = `disk-container-${disk.id}`;
      container.dataset.id = disk.id;
      container.dataset.itemType = 'disk';
      container.addEventListener('contextmenu', (event) => {
        event.stopPropagation();
        const items = [
          { label: '创建新分组', action: () => promptAndCreateGroup() },
          { type: 'separator' },
          { label: '重命名圆盘', action: () => sketchup.prompt_and_rename_radial_item('disk', disk.id, disk.name) },
          { label: '删除圆盘', action: () => sketchup.delete_radial_item('disk', disk.id) }
        ];
        showContextMenu(event, items);
      });
      const header = document.createElement('div');
      header.className = 'disk-header';
      header.onclick = () => toggleDiskEditor(disk.id);
      header.draggable = true;
      header.addEventListener('dragstart', (event) => {
        if (event.target.tagName.toLowerCase() === 'button') {
          event.preventDefault();
          return;
        }
        draggedItem = container;
        event.dataTransfer.setData('one-toolbar/radial-item', container.dataset.id);
        event.dataTransfer.effectAllowed = 'move';
        setTimeout(() => container.classList.add('is-dragging'), 0);
      });

      header.addEventListener('dragend', () => {
        if (draggedItem) {
          draggedItem.classList.remove('is-dragging');
        }
        document.getElementById('radial-list-container').querySelectorAll('.disk-container, .group-container').forEach(el => {
          el.classList.remove('drag-over-top', 'drag-over-bottom', 'drag-over-inside');
        });
        draggedItem = null;
      });

      // --- 【核心修改】创建包含图标按钮和名称的容器 ---
      const nameContainer = document.createElement('div');
      nameContainer.style.display = 'flex';
      nameContainer.style.alignItems = 'center';
      nameContainer.style.gap = '8px';

      const iconBtn = document.createElement('button');
      iconBtn.className = 'center-icon-btn'; // 可以为它添加特定样式
      iconBtn.style.cssText = 'width: 24px; height: 24px; padding: 0; border-radius: 4px; border: 1px solid var(--border-color); cursor: pointer; flex-shrink: 0; display: flex; align-items: center; justify-content: center;';
      iconBtn.onclick = (e) => { e.stopPropagation(); promptAndSetCenterIcon(disk.id); };

      if (disk.center_icon_data_uri) {
        iconBtn.innerHTML = `<img src="${disk.center_icon_data_uri}" style="width: 100%; height: 100%; object-fit: cover;">`;
      } else {
        iconBtn.innerHTML = '◕';
        iconBtn.style.fontSize = '18px';
      }
      
      const nameSpan = document.createElement('span');
      nameSpan.className = 'toggle-expand-btn';
      nameSpan.textContent = disk.name;
      
      nameContainer.appendChild(iconBtn);
      nameContainer.appendChild(nameSpan);
      // --- 修改结束 ---
      const controls = document.createElement('div');
      controls.className = 'item-actions';
      controls.onclick = e => e.stopPropagation();
      const shortcutBtn = document.createElement('button');
      
      if (disk.shortcut) {
        shortcutBtn.textContent = `快捷键: ${disk.shortcut}`;
        shortcutBtn.classList.add('has-shortcut');
      } else {
        shortcutBtn.textContent = '设置快捷键';
        shortcutBtn.classList.add('no-shortcut');
      }
      
      shortcutBtn.onclick = () => sketchup.open_shortcut_settings(disk.name);
      controls.appendChild(shortcutBtn);
      header.appendChild(nameContainer);
      header.appendChild(controls);

      const body = document.createElement('div');
      body.className = 'disk-body';

      const editorGrid = document.createElement('div');
      editorGrid.className = 'editor-grid';

      const quickActionBar = document.createElement('div');
      quickActionBar.className = 'quick-action-bar-config';
      
      renderQuickActionBar(quickActionBar, disk);

      const editorView = document.createElement('div');
      editorView.className = 'radial-editor';

      const layoutSettings = document.createElement('div');
      layoutSettings.className = 'layout-settings';
      layoutSettings.innerHTML = '<h5>布局设置</h5>';

      const ringConfigs = [
        { label: '内环插槽数', min: 4, max: 8 },
        { label: '中环插槽数', min: 6, max: 12 },
        { label: '外环插槽数', min: 8, max: 16 }
      ];
      const slotCounts = disk.slot_counts || [8, 12, 16];
      const qaSlotCount = disk.quick_action_slot_count || 5;

      ringConfigs.forEach((config, index) => {
        const sliderContainer = createSliderControl(
          config.label, 
          slotCounts[index], 
          config.min,
          config.max,
          (value) => {
            const newRingCounts = [...slotCounts];
            newRingCounts[index] = value;
            if(newRingCounts[index] < config.min) newRingCounts[index] = config.min;
            if(newRingCounts[index] > config.max) newRingCounts[index] = config.max;
            sketchup.update_disk_layout(disk.id, newRingCounts, qaSlotCount);
          }
        );
        layoutSettings.appendChild(sliderContainer);
      });

      const qaSliderContainer = createSliderControl(
        '快捷条插槽数',
        qaSlotCount,
        6, 12,
        (value) => {
          sketchup.update_disk_layout(disk.id, slotCounts, value);
        }
      );
      layoutSettings.appendChild(qaSliderContainer);
      const startAngleContainer = document.createElement('div');
      startAngleContainer.className = 'toggle-control';
      
      const startAtTop = disk.start_at_top || false;
      const switchId = `start-angle-switch-${disk.id}`;
      
      const switchLabel = document.createElement('label');
      switchLabel.htmlFor = switchId;
      switchLabel.textContent = '上方对齐';

      const switchWrapper = document.createElement('label');
      switchWrapper.className = 'toggle-switch';

      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.id = switchId;
      checkbox.checked = startAtTop;
      checkbox.onchange = () => {
        const isChecked = checkbox.checked;
        
        sketchup.set_disk_start_angle(disk.id, isChecked);

        disk.start_at_top = isChecked;

        const container = document.getElementById(`disk-container-${disk.id}`);
        const editorView = container.querySelector('.radial-editor');
        const ringsContainer = editorView.querySelector('.editor-rings-container');

        if (container.classList.contains('is-expanded') && ringsContainer) {
          renderSlots(ringsContainer, disk);
        }
      };

      const sliderSpan = document.createElement('span');
      sliderSpan.className = 'slider round';

      switchWrapper.appendChild(checkbox);
      switchWrapper.appendChild(sliderSpan);
      
      startAngleContainer.appendChild(switchLabel);
      startAngleContainer.appendChild(switchWrapper);
      
      layoutSettings.appendChild(startAngleContainer);
      
      editorGrid.appendChild(quickActionBar);
      editorGrid.appendChild(editorView);
      editorGrid.appendChild(layoutSettings);
      
      body.appendChild(editorGrid);

      // ==================================================================================
      // ▼▼▼ 这里是我们要修改的核心区域 ▼▼▼
      // ==================================================================================
      const preview = document.createElement('div');
      preview.className = 'linear-preview';

      // 1. 提取出所有环的图标，并过滤掉空环
      const rings = (disk.rings || []).map(ring => (ring || []).filter(Boolean)).filter(ring => ring.length > 0);
      // 2. 提取出快捷启动条的图标
      const quickActions = (disk.quick_actions || []).filter(Boolean);

      // 3. 将所有图标部分合并到一个数组中，用 null 作为分隔符的占位符
      let allPreviewItems = [];
      rings.forEach((ring, index) => {
        allPreviewItems.push(...ring);
        // 如果不是最后一个环，并且后面还有其他内容（其他环或快捷启动条），则添加一个分隔符
        if (index < rings.length - 1 || (index === rings.length - 1 && quickActions.length > 0)) {
          allPreviewItems.push(null); // 使用 null 作为分隔符的标记
        }
      });
      // 最后添加快捷启动条的图标
      allPreviewItems.push(...quickActions);

      // 4. 遍历这个整合后的数组来创建DOM元素
      allPreviewItems.forEach(item => {
        if (item) { // 如果 item 不是 null，说明它是一个命令
          const cmdEl = createCommandElement(item, true, null, null);
          cmdEl.draggable = false;
          cmdEl.style.cursor = 'default';
          cmdEl.oncontextmenu = (e) => e.preventDefault();
          preview.appendChild(cmdEl);
        } else { // 如果 item 是 null，说明我们应该在这里创建一个分隔符
          const sepEl = document.createElement('div');
          sepEl.className = 'separator';
          // 为预览区的分隔符设置一些特定的样式，让它更细、更不显眼
          sepEl.style.width = '2px';
          sepEl.style.height = '32px';
          sepEl.style.margin = '4px 4px';
          sepEl.style.alignSelf = 'center';
          sepEl.style.backgroundColor = 'var(--item-border-color)';
          sepEl.style.opacity = '1.0';
          preview.appendChild(sepEl);
        }
      });
      // ======================== ▲▲▲ 核心修改结束 ▲▲▲ ========================

      container.appendChild(header);
      container.appendChild(preview);
      container.appendChild(body);
      return container;
    }
    
    function createGroupElement(group) {
        const container = document.createElement('div');
        container.className = 'group-container';
        container.dataset.id = group.id;
        container.dataset.itemType = 'group';
    
        // 右键菜单逻辑保持不变
        container.addEventListener('contextmenu', (event) => {
          event.stopPropagation();
          const items = [
            { label: '重命名分组', action: () => sketchup.prompt_and_rename_radial_item('group', group.id, group.name) },
            { label: '删除分组', action: () => sketchup.delete_radial_item('group', group.id) },
            { type: 'separator' },
            { label: '创建新分组', action: () => promptAndCreateGroup() }
          ];
          showContextMenu(event, items);
        });
        
        // 分组头部的创建和拖拽逻辑保持不变
        const header = document.createElement('div');
        header.className = 'group-header';
        header.draggable = true;
        header.addEventListener('dragstart', (event) => {
          draggedItem = container;
          event.dataTransfer.setData('one-toolbar/radial-item', container.dataset.id);
          event.dataTransfer.effectAllowed = 'move';
          setTimeout(() => container.classList.add('is-dragging'), 0);
        });
        header.addEventListener('dragend', () => {
          if (draggedItem) {
            draggedItem.classList.remove('is-dragging');
          }
          document.getElementById('radial-list-container').querySelectorAll('.disk-container, .group-container').forEach(el => {
            el.classList.remove('drag-over-top', 'drag-over-bottom', 'drag-over-inside');
          });
          draggedItem = null;
        });
        const nameSpan = document.createElement('span');
        nameSpan.textContent = group.name;
        header.appendChild(nameSpan);
        
        // 用来容纳圆盘的列表容器
        const diskList = document.createElement('div');
        diskList.className = 'disk-list-in-group';
        
        container.appendChild(header);
        container.appendChild(diskList);
    
        // --- 【核心修正】---
        // 不再需要去外部的 diskMap 查找，直接遍历 group 对象自带的 'disks' 数组
        (group.disks || []).forEach(diskData => {
            // group.disks 数组中的每个元素都已经是完整的圆盘对象了
            if (diskData) {
              diskList.appendChild(createDiskContainerElement(diskData));
            }
        });
    
        // 为分组内部的列表添加拖放事件监听
        addDragDropHandlersToGroupList(diskList, group.id);
    
        return container;
    }

    function copyTextToClipboard(text) {
      if (!navigator.clipboard) {
        console.error("剪贴板API在此环境中不可用。");
        return;
      }

      navigator.clipboard.writeText(text).then(() => {
        const feedback = `命令名称 '${text}' 已复制到剪贴板`;
        sketchup.show_status_feedback(feedback);
      }).catch(err => {
        console.error('无法复制文本到剪贴板: ', err);
      });
    }

    function findDiskDataById(diskId) {
        const { allItems = [] } = allRadialData;
        for (const item of allItems) {
            // 检查顶层项目
            if (item.id === diskId && item.rings) {
                return item; // 这是一个顶层圆盘，直接返回
            }
            // 如果是分组，检查其内部的disks数组
            if (item.disks) {
                const foundDisk = item.disks.find(d => d.id === diskId);
                if (foundDisk) {
                    return foundDisk; // 在分组内找到了，返回
                }
            }
        }
        return null; // 遍历完成仍未找到
    }

    function setPendingDiskToExpand(diskId) {
      pendingDiskToExpandId = diskId;
    }

    function toggleDiskEditor(diskId, forceOpen = false) { 
        const container = document.getElementById(`disk-container-${diskId}`);
        if (!container) return;
    
        const isCurrentlyExpanded = container.classList.contains('is-expanded');
        const openEditor = forceOpen ? true : !isCurrentlyExpanded; 
    
        container.classList.toggle('is-expanded', openEditor);
    
        if (openEditor) {
            container.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    
            // --- 【核心修正】---
            // 不再从 allRadialData.disks 查找，而是使用我们新的辅助函数
            const disk = findDiskDataById(diskId); 
            
            const editorView = container.querySelector('.radial-editor');
    
            if (disk && editorView) {
                editorView.innerHTML = `<div class="editor-rings-container"></div>`;
                const ringsContainer = editorView.querySelector('.editor-rings-container');
                renderSlots(ringsContainer, disk);
            } else {
                console.error(`无法为ID为 ${diskId} 的圆盘找到数据，无法渲染编辑器。`);
            }
        }
    }

    function renderSlots(ringsContainer, disk) {
        ringsContainer.innerHTML = '';
        const radii = [70, 115, 160];
        const numSlotsArray = disk.slot_counts || [8, 12, 16];
        const ringData = disk.rings || [];
        const angleOffset = disk.start_at_top ? -Math.PI / 2 : 0;

        numSlotsArray.forEach((numSlots, ringIndex) => {
            if (numSlots === 0) return;
            const radius = radii[ringIndex];
            const diameter = radius * 2;
            const ringEl = document.createElement('div');
            ringEl.className = `editor-ring ring-${ringIndex}`;
            ringEl.style.width = `${diameter}px`;
            ringEl.style.height = `${diameter}px`;
            ringsContainer.appendChild(ringEl);
            const currentRingData = ringData[ringIndex] || [];

            for (let i = 0; i < numSlots; i++) {
                const angleRad = angleOffset + (2 * Math.PI * i / numSlots);
                const slotEl = document.createElement('div');
                slotEl.className = 'command-slot';
                slotEl.dataset.slot = i;
                const x = radius * Math.cos(angleRad);
                const y = radius * Math.sin(angleRad);
                slotEl.style.transform = `translate(${x}px, ${y}px)`;

                const cmd = currentRingData[i];

                slotEl.addEventListener('contextmenu', (event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    
                    let menuItems = [];

                    if (cmd) {
                        menuItems = [
                            { label: '在此处插入新插槽', action: () => sketchup.insert_radial_slot(disk.id, ringIndex, i) },
                            { type: 'separator' },
                            { label: '删除此图标', action: () => sketchup.remove_command_from_disk(disk.id, ringIndex, i) },
                            { label: '删除此图标和插槽', action: () => sketchup.delete_radial_slot(disk.id, ringIndex, i) }
                        ];
                    } else {
                        menuItems = [
                            { label: '在此处插入新插槽', action: () => sketchup.insert_radial_slot(disk.id, ringIndex, i) },
                            { label: '删除此空插槽', action: () => sketchup.delete_radial_slot(disk.id, ringIndex, i) }
                        ];
                    }
                    showContextMenu(event, menuItems);
                });

                if (cmd) {
                    const cmdEl = createCommandElement(cmd, true, null, { diskId: disk.id, ringIndex: ringIndex, slotIndex: i });
                    slotEl.appendChild(cmdEl);
                }
                addDropListenersToSlot(slotEl, disk.id, ringIndex, i);
                ringsContainer.appendChild(slotEl);
            }
        });
    }

    function renderQuickActionBar(container, disk) {
      container.innerHTML = '<h5>快捷启动条</h5>';
      
      const slotCount = disk.quick_action_slot_count || 0;
      const actions = disk.quick_actions || [];

      for (let i = 0; i < slotCount; i++) {
        const actionData = actions[i];
        
        const slotContainer = document.createElement('div');
        slotContainer.className = 'qa-slot';
        
        const keyInput = document.createElement('input');
        keyInput.type = 'text';
        keyInput.className = 'qa-key-input';
        keyInput.maxLength = 1;
        keyInput.value = actionData?.key || '';
        keyInput.oninput = function() {
            this.value = this.value.replace(/[^0-9\-=]/g, '');
        };
        keyInput.onchange = () => {
          sketchup.set_quick_action_key(disk.id, i, keyInput.value);
        };
        
        const dropZone = document.createElement('div');
        dropZone.className = 'qa-icon-dropzone';
        if (actionData) {
          dropZone.classList.add('has-command');
          const cmdEl = createCommandElement(actionData, true, null, { diskId: disk.id, slotIndex: i, isQuickAction: true });
          dropZone.appendChild(cmdEl);
        }
        
        dropZone.addEventListener('dragenter', e => { e.preventDefault(); dropZone.classList.add('drag-over'); });
        dropZone.addEventListener('dragleave', e => { dropZone.classList.remove('drag-over'); });
        dropZone.addEventListener('dragover', e => e.preventDefault());
        dropZone.addEventListener('drop', e => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
            const commandId = e.dataTransfer.getData('one-toolbar/command-item');
            if (!commandId) return;

            if (draggedFromSlotInfo && draggedFromSlotInfo.isQuickAction && draggedFromSlotInfo.diskId === disk.id) {
                const sourceSlotIndex = draggedFromSlotInfo.slotIndex;
                const targetSlotIndex = i;

                if (sourceSlotIndex === targetSlotIndex) {
                    return;
                }
                sketchup.move_or_swap_quick_action(disk.id, sourceSlotIndex, targetSlotIndex);
            } else {
                handleAddCommand(commandId, disk.id, i, null, false, false, true, false);
            }
        });

        slotContainer.addEventListener('contextmenu', (event) => {
          event.preventDefault();
          event.stopPropagation();
          const items = [
            { label: '清空此插槽', action: () => sketchup.remove_quick_action(disk.id, i) }
          ];
          showContextMenu(event, items);
        });

        slotContainer.appendChild(keyInput);
        slotContainer.appendChild(dropZone);
        container.appendChild(slotContainer);
      }
    }
    
    function createCommandElement(cmd, isCustom, toolbarName, slotInfo = null) {
      const item = document.createElement('div');
      item.className = 'command-item';
      if(slotInfo) {
          item.classList.add('in-slot');
      }
      if(isCustom) {
          item.classList.add('custom-command-item');
      }
    
      item.dataset.id = cmd.unique_id || cmd.id;
      item.title = cmd.tooltip;
      item.draggable = true;
    
      if (cmd.emoji) { 
        const emojiSpan = document.createElement('span');
        emojiSpan.className = 'emoji-icon';
        emojiSpan.textContent = cmd.emoji;
        item.appendChild(emojiSpan);
      } else {
        const img = document.createElement('img');
        img.src = cmd.icon_data_uri;
        item.appendChild(img);
      }
    
      // ==================================================================================
      // ▼▼▼ 【JS核心修正】在这里检查是否为文件夹，并直接创建真实的装饰器元素 ▼▼▼
      // ==================================================================================
      if (cmd.type === 'folder') {
          item.classList.add('folder-item'); // 将添加class的逻辑也集中到这里
          const decorator = document.createElement('span');
          decorator.className = 'folder-decorator';
          decorator.textContent = '🗀';
          item.appendChild(decorator);
      }
      // ======================== ▲▲▲ 核心修正结束 ▲▲▲ ========================
    
      item.addEventListener('dragstart', e => {
        // ... (此处的拖拽事件监听器保持不变)
        e.stopPropagation();
        draggedId = cmd.unique_id;
        draggedFromSlotInfo = slotInfo;
        const parentDock = item.closest('.command-list[data-dock-id]');
        const parentToolbar = item.closest('.command-list[data-toolbar-name]');
        if (parentDock) {
            draggedFromDockInfo = { dockId: parentDock.dataset.dockId, itemId: cmd.unique_id || cmd.id };
            draggedFromToolbarInfo = null;
        } else if(parentToolbar) {
            draggedFromToolbarInfo = { toolbarName: parentToolbar.dataset.toolbarName, itemId: cmd.unique_id };
            draggedFromDockInfo = null;
        } else {
            draggedFromDockInfo = null;
            draggedFromToolbarInfo = null;
        }
        e.dataTransfer.setData('one-toolbar/command-item', cmd.unique_id);
        e.dataTransfer.effectAllowed = 'move';
        setTimeout(() => item.classList.add('dragging'), 0);
      });
    
      item.addEventListener('dragend', e => {
          // ... (此处的拖拽事件监听器保持不变)
          item.classList.remove('dragging');
          draggedFromDockInfo = null;
          draggedFromToolbarInfo = null;
          const sourcePanel = document.getElementById('source-panel');
          const sourceList = document.getElementById('source-list');
          const sourceRect = sourcePanel.getBoundingClientRect();
          if (sourceList.classList.contains('delete-zone-active') &&
              e.clientX >= sourceRect.left && e.clientX <= sourceRect.right &&
              e.clientY >= sourceRect.top && e.clientY <= sourceRect.bottom) {
              if (draggedFromSlotInfo) {
                  if (draggedFromSlotInfo.isQuickAction) {
                      sketchup.remove_quick_action(draggedFromSlotInfo.diskId, draggedFromSlotInfo.slotIndex);
                  } else {
                      sketchup.remove_command_from_disk(draggedFromSlotInfo.diskId, draggedFromSlotInfo.ringIndex, draggedFromSlotInfo.slotIndex);
                  }
              } else if (draggedFromCustom) {
                  sketchup.delete_command(toolbarName, draggedId);
              }
          }
          sourceList.classList.remove('delete-zone-active');
          draggedId = null;
          draggedFromCustom = false;
          draggedFromSlotInfo = null;
      });
    
      return item;
    }

    function addDropListenersToToolbar(element) {
        element.addEventListener('dragover', e => {
            if (e.dataTransfer.types.includes('one-toolbar/command-item') || e.dataTransfer.types.includes('one-toolbar/custom-separator-item')) {
              e.preventDefault();
              e.stopPropagation();
    
              const container = element;
              container.querySelectorAll('.command-item, .separator').forEach(el => el.classList.remove('drag-over-before', 'drag-over-after'));
              const draggableItems = Array.from(container.children).filter(c => !c.classList.contains('dragging'));
              if (draggableItems.length === 0) return;
    
              const dropX = e.clientX;
              let closest = null;
              let minDistance = Infinity;
    
              draggableItems.forEach(child => {
                  const rect = child.getBoundingClientRect();
                  const midX = rect.left + rect.width / 2;
                  const distance = Math.abs(midX - dropX); // For horizontal list, only X matters
                  if (distance < minDistance) {
                      minDistance = distance;
                      closest = child;
                  }
              });
    
              if (closest) {
                  const rect = closest.getBoundingClientRect();
                  if (dropX < rect.left + rect.width / 2) {
                      closest.classList.add('drag-over-before');
                  } else {
                      closest.classList.add('drag-over-after');
                  }
              }
            }
        });
    
        // dragleave 事件监听器保持不变
        element.addEventListener('dragleave', e => { 
            element.querySelectorAll('.command-item, .separator').forEach(el => el.classList.remove('drag-over-before', 'drag-over-after'));
        });
    
        element.addEventListener('drop', e => { 
            e.stopPropagation(); 
            e.preventDefault();
            element.querySelectorAll('.command-item, .separator').forEach(el => el.classList.remove('drag-over-before', 'drag-over-after'));
            
            const targetToolbarName = element.dataset.toolbarName;
            const draggedCommandId = e.dataTransfer.getData('one-toolbar/command-item');
            const draggedSeparatorId = e.dataTransfer.getData('one-toolbar/custom-separator-item');
            const draggedItemId = draggedCommandId || draggedSeparatorId;
    
            if (!draggedItemId) return;
    
            // 计算放置位置
            let targetItemId = null;
            let isAfter = false;
            const draggableItems = Array.from(element.children).filter(c => !c.classList.contains('dragging'));
            if (draggableItems.length > 0) {
                const dropX = e.clientX;
                let closest = null;
                let minDistance = Infinity;
                draggableItems.forEach(child => {
                    const rect = child.getBoundingClientRect();
                    const midX = rect.left + rect.width / 2;
                    const distance = Math.abs(midX - dropX);
                    if (distance < minDistance) {
                        minDistance = distance;
                        closest = child;
                    }
                });
                if (closest) {
                    targetItemId = closest.dataset.id;
                    const rect = closest.getBoundingClientRect();
                    isAfter = dropX >= rect.left + rect.width / 2;
                }
            }
    
            // --- 【核心修改】在这里重构拖放判断逻辑 ---
            if (draggedFromToolbarInfo && draggedFromToolbarInfo.toolbarName === targetToolbarName) {
                // 情况1: 在同一个工具栏内部重新排序 (图标或分隔符)
                sketchup.reorder_toolbar_item(targetToolbarName, draggedItemId, targetItemId, isAfter);
            
            } else if (draggedFromToolbarInfo && draggedFromToolbarInfo.toolbarName !== targetToolbarName) {
                // 情况2: 从一个自定义工具栏移动到另一个 (图标或分隔符)
                sketchup.move_item_between_custom_toolbars(draggedFromToolbarInfo.toolbarName, targetToolbarName, draggedItemId, targetItemId, isAfter);
    
            } else if (draggedCommandId) {
                // 情况3: 从左侧“所有可用命令”列表拖入一个新图标
                handleAddCommand(draggedCommandId, targetToolbarName, targetItemId, isAfter, false, false, false, false);
            }
        });
    }

    function addDropListenersToDockBar(element) {
      element.addEventListener('dragenter', e => { 
          if (e.dataTransfer.types.includes('one-toolbar/command-item') || e.dataTransfer.types.includes('one-toolbar/separator-item')) {
            e.preventDefault(); 
            element.classList.add('drag-over'); 
          }
      });
      element.addEventListener('dragleave', e => { 
          element.classList.remove('drag-over'); 
      });
     
      element.addEventListener('dragover', e => {
          if (e.dataTransfer.types.includes('one-toolbar/command-item') || e.dataTransfer.types.includes('one-toolbar/separator-item')) {
            e.preventDefault();
            e.stopPropagation();
      
            const container = element;
            container.querySelectorAll('.command-item, .separator').forEach(el => el.classList.remove('drag-over-before', 'drag-over-after'));
      
            const draggableItems = Array.from(container.children).filter(c => !c.classList.contains('dragging'));
            const dropX = e.clientX;
            const dropY = e.clientY;
      
            // 如果容器为空，就不计算最近元素，直接返回
            if (draggableItems.length === 0) return;
     
            let closest = null;
            let minDistance = Infinity;
      
            draggableItems.forEach(child => {
                const rect = child.getBoundingClientRect();
                const midX = rect.left + rect.width / 2;
                const midY = rect.top + rect.height / 2;
                const distance = Math.sqrt(Math.pow(midX - dropX, 2) + Math.pow(midY - dropY, 2));
      
                if (distance < minDistance) {
                    minDistance = distance;
                    closest = child;
                }
            });
      
            if (closest) {
                const rect = closest.getBoundingClientRect();
                if (dropX < rect.left + rect.width / 2) {
                    closest.classList.add('drag-over-before');
                } else {
                    closest.classList.add('drag-over-after');
                }
            }
          }
      });
      
        element.addEventListener('drop', e => { 
            e.stopPropagation(); 
            e.preventDefault();
            element.classList.remove('drag-over');
            element.querySelectorAll('.command-item, .separator').forEach(el => el.classList.remove('drag-over-before', 'drag-over-after'));
        
            // --- 【核心修改】在这里重构了拖放逻辑 ---
            
            const targetDockId = element.dataset.dockId;
            const draggedItemId = e.dataTransfer.getData('one-toolbar/command-item') || e.dataTransfer.getData('one-toolbar/separator-item');
            if (!draggedItemId) return;
     
            // 计算放置位置（这部分逻辑不变）
            let targetItemId = null;
            let isAfter = false;
            const draggableItems = Array.from(element.children).filter(c => !c.classList.contains('dragging'));
            if (draggableItems.length > 0) {
                const dropX = e.clientX;
                const dropY = e.clientY;
                let closest = null;
                let minDistance = Infinity;
                draggableItems.forEach(child => {
                    const rect = child.getBoundingClientRect();
                    const midX = rect.left + rect.width / 2;
                    const midY = rect.top + rect.height / 2;
                    const distance = Math.sqrt(Math.pow(midX - dropX, 2) + Math.pow(midY - dropY, 2));
                    if (distance < minDistance) {
                        minDistance = distance;
                        closest = child;
                    }
                });
                if (closest) {
                    targetItemId = closest.dataset.id;
                    const rect = closest.getBoundingClientRect();
                    isAfter = dropX >= rect.left + rect.width / 2;
                }
            }
        
            // 判断拖放的三种情况
            if (draggedFromDockInfo && draggedFromDockInfo.dockId === targetDockId) {
                // 情况1: 在同一个DOCK栏内部重新排序
                sketchup.reorder_dock_item(targetDockId, draggedItemId, targetItemId, isAfter);
     
            } else if (draggedFromDockInfo && draggedFromDockInfo.dockId !== targetDockId) {
                // 情况2: 从一个DOCK栏移动到另一个DOCK栏
                sketchup.move_item_between_docks(draggedFromDockInfo.dockId, targetDockId, draggedItemId, targetItemId, isAfter);
     
            } else {
                // 情况3: 从左侧“所有可用命令”列表拖入
                handleAddCommand(draggedItemId, targetDockId, targetItemId, isAfter, false, false, false, true);
            }
        });
      }

    function addDragDropHandlersToRadialList(container) {
      let draggedItem = null;
      let lastDragOverElement = null;

      container.querySelectorAll('.disk-container, .group-container').forEach(item => {
        item.addEventListener('dragstart', (event) => {
          const targetTag = event.target.tagName.toLowerCase();
          if (['input', 'button', 'select'].includes(targetTag)) {
            event.preventDefault();
            return;
          }
          
          event.stopPropagation();
          draggedItem = event.currentTarget;
          event.dataTransfer.setData('one-toolbar/radial-item', draggedItem.dataset.id);
          event.dataTransfer.effectAllowed = 'move';
          setTimeout(() => draggedItem.classList.add('is-dragging'), 0);
        });
        item.addEventListener('dragend', () => {
          if (draggedItem) {
            draggedItem.classList.remove('is-dragging');
          }
          if (lastDragOverElement) {
            lastDragOverElement.classList.remove('drag-over-top', 'drag-over-bottom', 'drag-over-inside');
          }
          draggedItem = null;
          lastDragOverElement = null;
        });
      });

      container.addEventListener('dragover', (event) => {
        event.preventDefault();
        const targetItem = event.target.closest('.disk-container, .group-container, .group-header');
        const draggedType = draggedItem?.dataset.itemType;

        if (lastDragOverElement && lastDragOverElement !== targetItem) {
          lastDragOverElement.classList.remove('drag-over-top', 'drag-over-bottom', 'drag-over-inside');
        }
        lastDragOverElement = targetItem;
        if (!targetItem || !draggedItem || targetItem === draggedItem) return;

        const rect = targetItem.getBoundingClientRect();
        
        if (draggedType === 'disk' && targetItem.classList.contains('group-container')) {
            targetItem.classList.add('drag-over-inside');
            targetItem.classList.remove('drag-over-top', 'drag-over-bottom');
        } else {
            targetItem.classList.remove('drag-over-inside');
            const isOverTopHalf = event.clientY < rect.top + rect.height / 2;
            if (isOverTopHalf) {
              targetItem.classList.add('drag-over-top');
              targetItem.classList.remove('drag-over-bottom');
            } else {
              targetItem.classList.add('drag-over-bottom');
              targetItem.classList.remove('drag-over-top');
            }
        }
      });
      
      container.addEventListener('drop', (event) => {
        event.preventDefault();
        if (!draggedItem) return;
        const targetItem = event.target.closest('.disk-container, .group-container');
        
        if (lastDragOverElement) {
            lastDragOverElement.classList.remove('drag-over-top', 'drag-over-bottom', 'drag-over-inside');
        }

        const data = { draggedId: draggedItem.dataset.id, targetId: null, position: 'after' };

        if (targetItem) {
          data.targetId = targetItem.dataset.id;
          const rect = targetItem.getBoundingClientRect();
          if (draggedItem.dataset.itemType === 'disk' && targetItem.classList.contains('group-container')) {
              data.position = 'inside';
          } else {
              data.position = (event.clientY < rect.top + rect.height / 2) ? 'before' : 'after';
          }
        }
        
        sketchup.update_radial_item_position(data);
      });
    }

    function addDragDropHandlersToGroupList(groupListElement, groupId) {
      let draggedItem = null;

      groupListElement.querySelectorAll('.disk-container').forEach(item => {
        item.addEventListener('dragstart', (event) => {
          event.stopPropagation();
          draggedItem = event.currentTarget;
          event.dataTransfer.setData('one-toolbar/radial-item', draggedItem.dataset.id);
          event.dataTransfer.effectAllowed = 'move';
          setTimeout(() => draggedItem.classList.add('is-dragging'), 0);
        });
        item.addEventListener('dragend', () => {
          if (draggedItem) {
            draggedItem.classList.remove('is-dragging');
          }
          groupListElement.querySelectorAll('.disk-container').forEach(el => {
            el.classList.remove('drag-over-top', 'drag-over-bottom');
          });
          draggedItem = null;
        });
      });

      groupListElement.addEventListener('dragover', (event) => {
        if (event.dataTransfer.types.includes('one-toolbar/radial-item')) {
          event.preventDefault();
          event.stopPropagation();
          const targetItem = event.target.closest('.disk-container');
          if (targetItem && targetItem !== draggedItem) {
            const rect = targetItem.getBoundingClientRect();
            const isOverTopHalf = event.clientY < rect.top + rect.height / 2;
            
            groupListElement.querySelectorAll('.disk-container').forEach(el => {
              el.classList.remove('drag-over-top', 'drag-over-bottom');
            });

            if (isOverTopHalf) {
              targetItem.classList.add('drag-over-top');
            } else {
              targetItem.classList.add('drag-over-bottom');
            }
          }
        }
      });

      groupListElement.addEventListener('drop', (event) => {
        if (event.dataTransfer.types.includes('one-toolbar/radial-item')) {
          event.preventDefault();
          event.stopPropagation();
          
          const draggedId = event.dataTransfer.getData('one-toolbar/radial-item');
          const targetItem = event.target.closest('.disk-container');
          
          groupListElement.querySelectorAll('.disk-container').forEach(el => {
            el.classList.remove('drag-over-top', 'drag-over-bottom');
          });

          if (!targetItem || draggedId === targetItem.dataset.id) return;

          let currentIds = Array.from(groupListElement.children).map(el => el.dataset.id);
          currentIds = currentIds.filter(id => id !== draggedId);
          
          const targetIndex = currentIds.indexOf(targetItem.dataset.id);
          const rect = targetItem.getBoundingClientRect();
          const isDroppedOnTopHalf = event.clientY < rect.top + rect.height / 2;
          
          if (isDroppedOnTopHalf) {
            currentIds.splice(targetIndex, 0, draggedId);
          } else {
            currentIds.splice(targetIndex + 1, 0, draggedId);
          }
          
          sketchup.reorder_disks_in_group(groupId, currentIds);
        }
      });
    }

    function addDropListenersToSlot(slot, diskId, ringIndex, slotIndex) {
        slot.addEventListener('dragenter', e => { e.preventDefault(); slot.classList.add('drag-over'); });
        slot.addEventListener('dragleave', e => { slot.classList.remove('drag-over'); });
        slot.addEventListener('dragover', e => { e.preventDefault(); });
        slot.addEventListener('drop', e => {
            e.preventDefault();
            slot.classList.remove('drag-over');
            const commandId = e.dataTransfer.getData('one-toolbar/command-item');
            if (!commandId) return;

            if (draggedFromSlotInfo && 
                !draggedFromSlotInfo.isQuickAction &&
                draggedFromSlotInfo.diskId === diskId) {

                const sourceRingIndex = draggedFromSlotInfo.ringIndex;
                const sourceSlotIndex = draggedFromSlotInfo.slotIndex;
                const targetRingIndex = ringIndex;
                const targetSlotIndex = slotIndex;

                if (sourceRingIndex === targetRingIndex && sourceSlotIndex === targetSlotIndex) {
                    return;
                }
                sketchup.move_or_swap_radial_command(diskId, sourceRingIndex, sourceSlotIndex, targetRingIndex, targetSlotIndex);

            } else {
                handleAddCommand(commandId, diskId, ringIndex, slotIndex, false, true, false, false);
            }
        });
    }

    function findCommandDataById(commandId) {
        for (const toolbar of (allSourceData || [])) {
            const command = toolbar.commands.find(c => c.unique_id === commandId);
            if (command) return command;
        }
        for (const toolbar of (allCustomToolbarData || [])) {
          const command = (toolbar.commands || []).find(c => c.unique_id === commandId);
          if (command) return command;
        }
        for (const disk of (allRadialData.disks || [])) {
            const ringCommand = (disk.rings || []).flat().find(c => c && c.unique_id === commandId);
            if (ringCommand) return ringCommand;
            const qaCommand = (disk.quick_actions || []).find(c => c && c.unique_id === commandId);
            if (qaCommand) return qaCommand;
        }
        if (typeof allDockBarData !== 'undefined') {
            for (const dockBar of (allDockBarData || [])) {
                const command = (dockBar.commands || []).find(c => c.unique_id === commandId);
                if (command) return command;
            }
        }
        return null;
    }

    function handleAddCommand(commandId, targetName, arg1, arg2, isQuickToolbar, isRadialDisk, isQuickAction, isDockBar) {
        const commandData = findCommandDataById(commandId);
        if (!commandData) return;
        
        const executeCallback = (base64Png) => {
            if (isDockBar) {
                sketchup.add_command_to_dock_bar(targetName, commandId, arg1, arg2, base64Png);
            } else if (isRadialDisk) {
                sketchup.add_command_to_disk(targetName, arg1, arg2, commandId, base64Png);
            } else if (isQuickAction) {
                sketchup.set_quick_action_command(targetName, arg1, commandId, base64Png);
            } else {
                sketchup.add_or_reorder_command(targetName, commandId, arg1, arg2, base64Png);
            }
        };
    
        const img = new Image();
        img.onload = () => {
            const canvas = document.createElement('canvas');
            canvas.width = 96; 
            canvas.height = 96;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0, 96, 96);
            executeCallback(canvas.toDataURL('image/png').split(',')[1]);
        };
        
        img.onerror = () => {
            console.error("无法加载图标: ", commandData.icon_data_uri);
            executeCallback(null);
        }
    
        img.src = commandData.icon_data_uri;
    }

    function promptAndCreateToolbar() {
      sketchup.prompt_and_create_toolbar();
    }
    
    function createNewToolbar() {
      const nameInput = document.getElementById('new_toolbar_name');
      const name = nameInput.value.trim();
      if(name) {
        sketchup.create_toolbar(name);
        nameInput.value = '';
      }
    }
    function promptAndCreateDisk() {
        sketchup.prompt_and_create_disk();
    }
    function promptAndCreateDockBar() {
      sketchup.prompt_and_create_dock_bar();
    }
    function setPendingDiskToExpand(diskId) {
      pendingDiskToExpandId = diskId;
    }
    function handleNewDiskCreated(newDiskId) {
        // 当Ruby通知我们一个新圆盘已创建时：
        // 1. 我们不再直接调用toggleDiskEditor，因为此时DOM可能还未更新。
        // 2. 我们将这个新ID存入专用的全局变量 pendingDiskToExpandId。
        pendingDiskToExpandId = newDiskId;
        
        // 3. 在`renderRadialMenuTab`函数的末尾，已经有逻辑会检查这个变量。
        //    当`renderRadialMenuTab`下次因为数据刷新而被调用时，它会自动处理展开。
        //    这里的刷新是由Ruby端的 create_radial_disk 方法在最后调用的 refresh_ui 触发的。
    }

    let contextTarget = null;

    function showContextMenu(event, items) {
      event.preventDefault();
      const menu = document.getElementById('context-menu');
      menu.innerHTML = '';

      items.forEach(item => {
        if (item.type === 'separator') {
          menu.appendChild(document.createElement('div')).className = 'context-menu-separator';
        } else {
          const menuItem = document.createElement('div');
          menuItem.className = 'context-menu-item';
          menuItem.textContent = item.label;
          menuItem.onclick = () => {
            item.action();
            hideContextMenu();
          };
          menu.appendChild(menuItem);
        }
      });

      menu.style.display = 'block';
      const rect = menu.getBoundingClientRect();
      const bodyRect = document.body.getBoundingClientRect();
      let top = event.clientY;
      let left = event.clientX;
      if (top + rect.height > bodyRect.height) {
        top = event.clientY - rect.height;
      }
      if (left + rect.width > bodyRect.width) {
        left = event.clientX - rect.width;
      }
      menu.style.top = `${top}px`;
      menu.style.left = `${left}px`;

      contextTarget = event.currentTarget;
    }

    function hideContextMenu() {
      document.getElementById('context-menu').style.display = 'none';
      contextTarget = null;
    }

    // 分组管理功能函数
    function toggleAllGroups() {
      const containers = document.querySelectorAll('.group-container');
      if (containers.length === 0) return;

      // 检查当前状态：如果大部分组是展开的，则收起所有组；否则展开所有组
      let expandedCount = 0;
      containers.forEach(container => {
        if (container.style.display !== 'none') {
          expandedCount++;
        }
      });

      const shouldCollapse = expandedCount > containers.length / 2;

      containers.forEach(container => {
        container.style.display = shouldCollapse ? 'none' : 'block';
        const header = container.previousElementSibling;
        if (header && header.classList.contains('group-header')) {
          const indicator = header.querySelector('.group-expand-indicator');
          if (indicator) indicator.textContent = shouldCollapse ? '▶' : '▼';
        }
      });
    }

    function promptAndCreateGroup() {
      const groupName = prompt('请输入新分组名称:');
      if (groupName && groupName.trim()) {
        console.log('Creating group:', groupName.trim());
        // 调用后端创建分组
        if (typeof sketchup !== 'undefined' && sketchup.create_plugin_group) {
          console.log('Calling backend create_plugin_group');
          sketchup.create_plugin_group(groupName.trim());
        } else {
          // 前端临时处理，实际需要后端支持
          console.log('Backend not available, showing alert');
          alert('创建分组功能需要后端支持');
        }
      }
    }

    function promptAndRenameGroup(oldName) {
      // 保护默认分组不被重命名
      if (oldName === '默认分组') {
        alert('默认分组不能重命名。');
        return;
      }

      const newName = prompt('请输入新的分组名称:', oldName);
      if (newName && newName.trim() && newName.trim() !== oldName) {
        // 调用后端重命名分组
        if (typeof sketchup !== 'undefined' && sketchup.rename_plugin_group) {
          sketchup.rename_plugin_group(oldName, newName.trim());
        } else {
          // 前端临时处理，实际需要后端支持
          alert('重命名分组功能需要后端支持');
        }
      }
    }

    function confirmAndDeleteGroup(groupName) {
      // 保护默认分组不被删除
      if (groupName === '默认分组') {
        alert('默认分组不能删除。');
        return;
      }

      if (confirm(`确定要删除分组 "${groupName}" 吗？\n\n删除分组不会删除其中的插件，插件将移动到"默认分组"中。`)) {
        // 调用后端删除分组
        if (typeof sketchup !== 'undefined' && sketchup.delete_plugin_group) {
          sketchup.delete_plugin_group(groupName);
        } else {
          // 前端临时处理，实际需要后端支持
          alert('删除分组功能需要后端支持');
        }
      }
    }

    // 工具栏管理功能函数
    function toggleToolbarVisibility(toolbarName) {
      console.log('Toggling toolbar visibility:', toolbarName);
      if (typeof sketchup !== 'undefined' && sketchup.toggle_toolbar_visibility) {
        sketchup.toggle_toolbar_visibility(toolbarName);
      } else {
        alert('显隐工具条功能需要后端支持');
      }
    }

    function showMoveToGroupDialog(toolbarName) {
      console.log('Moving toolbar to group:', toolbarName);

      // 获取所有可用的分组
      const allGroups = Object.keys(window.pluginGroups || {});
      const defaultGroups = ['一、综合类大插件集', '二、辅助的相关插件', '三、组织组件编辑类', '其他插件'];
      const customGroups = allGroups.filter(group => !defaultGroups.includes(group));
      const availableGroups = [...defaultGroups, ...customGroups];

      // 创建模态对话框
      const modal = document.createElement('div');
      modal.className = 'move-to-group-modal';
      modal.innerHTML = `
        <div class="modal-overlay"></div>
        <div class="modal-content">
          <div class="modal-header">
            <h3>移动工具栏到分组</h3>
            <button class="modal-close">&times;</button>
          </div>
          <div class="modal-body">
            <p>选择要将 "<strong>${toolbarName}</strong>" 移动到的分组：</p>
            <div class="group-list">
              ${availableGroups.map(group => `
                <div class="group-option" data-group="${group}">
                  <span class="group-name">${group}</span>
                </div>
              `).join('')}
            </div>
          </div>
          <div class="modal-footer">
            <button class="btn-cancel">取消</button>
          </div>
        </div>
      `;

      document.body.appendChild(modal);

      // 添加事件监听
      const closeModal = () => {
        document.body.removeChild(modal);
      };

      modal.querySelector('.modal-close').addEventListener('click', closeModal);
      modal.querySelector('.btn-cancel').addEventListener('click', closeModal);
      modal.querySelector('.modal-overlay').addEventListener('click', closeModal);

      // 分组选择事件
      modal.querySelectorAll('.group-option').forEach(option => {
        option.addEventListener('click', () => {
          const targetGroup = option.dataset.group;
          if (typeof sketchup !== 'undefined' && sketchup.move_toolbar_to_group) {
            sketchup.move_toolbar_to_group(toolbarName, targetGroup);
          } else {
            alert('移动到分组功能需要后端支持');
          }
          closeModal();
        });
      });
    }

    function confirmUninstallPlugin(toolbarName) {
      if (confirm(`确定要卸载插件 "${toolbarName}" 吗？\n\n警告：此操作将完全移除插件文件，无法撤销！`)) {
        console.log('Uninstalling plugin:', toolbarName);
        if (typeof sketchup !== 'undefined' && sketchup.uninstall_plugin) {
          sketchup.uninstall_plugin(toolbarName);
        } else {
          alert('卸载插件功能需要后端支持');
        }
      }
    }

    // 为源列表添加拖拽排序功能
    function addDragDropHandlersToSourceList(container) {
      let draggedElement = null;
      let draggedType = null; // 'group' 或 'toolbar'
      let draggedData = null;

      container.addEventListener('dragover', (e) => {
        if (!draggedElement) return;

        e.preventDefault();
        const targetElement = e.target.closest('.group-header, .toolbar-name-header');
        if (!targetElement || targetElement === draggedElement) return;

        // 清除所有拖拽指示器
        container.querySelectorAll('.drag-over-top, .drag-over-bottom').forEach(el => {
          el.classList.remove('drag-over-top', 'drag-over-bottom');
        });

        // 计算拖拽位置
        const rect = targetElement.getBoundingClientRect();
        const isOverTopHalf = e.clientY < rect.top + rect.height / 2;

        // 添加拖拽指示器
        if (isOverTopHalf) {
          targetElement.classList.add('drag-over-top');
        } else {
          targetElement.classList.add('drag-over-bottom');
        }
      });

      container.addEventListener('drop', (e) => {
        e.preventDefault();

        const targetElement = e.target.closest('.group-header, .toolbar-name-header');
        if (!targetElement || !draggedElement) return;

        // 清除拖拽指示器
        container.querySelectorAll('.drag-over-top, .drag-over-bottom').forEach(el => {
          el.classList.remove('drag-over-top', 'drag-over-bottom');
        });

        // 计算拖拽位置
        const rect = targetElement.getBoundingClientRect();
        const isOverTopHalf = e.clientY < rect.top + rect.height / 2;

        const targetType = targetElement.classList.contains('group-header') ? 'group' : 'toolbar';
        const targetData = targetType === 'group'
          ? { groupName: targetElement.dataset.groupName }
          : { toolbarName: targetElement.dataset.toolbarName, groupName: targetElement.dataset.groupName };

        // 调用后端处理拖拽排序
        console.log('Drag drop:', { draggedType, targetType, draggedData, targetData, isAfter: !isOverTopHalf });

        if (draggedType === 'group' && targetType === 'group') {
          // 分组排序
          if (typeof sketchup !== 'undefined' && sketchup.reorder_plugin_groups) {
            sketchup.reorder_plugin_groups(draggedData.groupName, targetData.groupName, !isOverTopHalf);
          } else {
            console.log('Backend method reorder_plugin_groups not available');
          }
        } else if (draggedType === 'toolbar' && targetType === 'toolbar') {
          // 同分组内工具栏排序
          if (draggedData.sourceGroup === targetData.groupName) {
            if (typeof sketchup !== 'undefined' && sketchup.reorder_toolbars_in_group) {
              sketchup.reorder_toolbars_in_group(draggedData.toolbarName, targetData.toolbarName, targetData.groupName, !isOverTopHalf);
            } else {
              console.log('Backend method reorder_toolbars_in_group not available');
            }
          } else {
            // 跨分组移动工具栏
            if (typeof sketchup !== 'undefined' && sketchup.move_toolbar_between_groups) {
              sketchup.move_toolbar_between_groups(draggedData.toolbarName, draggedData.sourceGroup, targetData.groupName, targetData.toolbarName, !isOverTopHalf);
            } else {
              console.log('Backend method move_toolbar_between_groups not available');
            }
          }
        } else if (draggedType === 'toolbar' && targetType === 'group') {
          // 工具栏移动到分组
          if (typeof sketchup !== 'undefined' && sketchup.move_toolbar_to_group_position) {
            sketchup.move_toolbar_to_group_position(draggedData.toolbarName, draggedData.sourceGroup, targetData.groupName, !isOverTopHalf);
          } else {
            console.log('Backend method move_toolbar_to_group_position not available');
          }
        }

        // 重置拖拽状态
        draggedElement = null;
        draggedType = null;
        draggedData = null;
      });

      container.addEventListener('dragleave', (e) => {
        // 只有当鼠标离开整个容器时才清除指示器
        if (!container.contains(e.relatedTarget)) {
          container.querySelectorAll('.drag-over-top, .drag-over-bottom').forEach(el => {
            el.classList.remove('drag-over-top', 'drag-over-bottom');
          });
        }
      });

      // 监听拖拽开始事件
      container.addEventListener('dragstart', (e) => {
        const groupHeader = e.target.closest('.group-header');
        const toolbarHeader = e.target.closest('.toolbar-name-header');

        if (groupHeader) {
          draggedElement = groupHeader;
          draggedType = 'group';
          draggedData = { groupName: groupHeader.dataset.groupName };
        } else if (toolbarHeader) {
          draggedElement = toolbarHeader;
          draggedType = 'toolbar';
          draggedData = {
            toolbarName: toolbarHeader.dataset.toolbarName,
            sourceGroup: toolbarHeader.dataset.groupName
          };
        }
      });

      container.addEventListener('dragend', () => {
        // 清除所有拖拽状态
        container.querySelectorAll('.drag-over-top, .drag-over-bottom').forEach(el => {
          el.classList.remove('drag-over-top', 'drag-over-bottom');
        });
        draggedElement = null;
        draggedType = null;
        draggedData = null;
      });
    }
    
    function showStatusFeedback(message, isError = false) {
      const feedbackEl = document.createElement('div');
      feedbackEl.textContent = message;
      feedbackEl.style.position = 'fixed';
      feedbackEl.style.bottom = '20px';
      feedbackEl.style.left = '50%';
      feedbackEl.style.transform = 'translateX(-50%)';
      feedbackEl.style.padding = '10px 20px';
      feedbackEl.style.borderRadius = '6px';
      feedbackEl.style.backgroundColor = isError ? 'rgba(220, 53, 69, 0.9)' : 'rgba(40, 167, 69, 0.9)';
      feedbackEl.style.color = 'white';
      feedbackEl.style.zIndex = '9999';
      feedbackEl.style.opacity = '0';
      feedbackEl.style.transition = 'opacity 0.5s';
    
      document.body.appendChild(feedbackEl);
      
      setTimeout(() => { feedbackEl.style.opacity = '1'; }, 10);
      
      setTimeout(() => {
        feedbackEl.style.opacity = '0';
        setTimeout(() => {
          document.body.removeChild(feedbackEl);
        }, 500);
      }, 3000);
    }

    window.addEventListener('click', hideContextMenu);
    window.onload = () => {
        // 默认使用白天模式，不再从localStorage读取主题设置
        try { sketchup.get_initial_data(); } catch(e) { console.error("Sketchup object not found."); }
    
        const sourcePanel = document.getElementById('source-panel');
        const sourceList = document.getElementById('source-list');
        
        sourcePanel.addEventListener('dragenter', (e) => {
            if (draggedFromCustom || draggedFromSlotInfo) {
                sourceList.classList.add('delete-zone-active');
            }
        });
    
        sourcePanel.addEventListener('dragover', (e) => {
            if (draggedFromCustom || draggedFromSlotInfo) {
                e.preventDefault();
            }
        });
    
        sourcePanel.addEventListener('dragleave', (e) => {
            if (e.relatedTarget && !sourcePanel.contains(e.relatedTarget)) {
                sourceList.classList.remove('delete-zone-active');
            }
        });
        addDropListenersToDockSlot(document.getElementById('dock-slot-top'), 'top');
        addDropListenersToDockSlot(document.getElementById('dock-slot-bottom'), 'bottom');
        addDropListenersToDockSlot(document.getElementById('dock-slot-right'), 'right');
        addDropListenerToDockBarList(document.getElementById('dock-bars-list-container'));
        const slotsArea = document.querySelector('.dock-slots-area');
        if (slotsArea) {
          slotsArea.addEventListener('click', (event) => {
            if (event.target.classList.contains('header-action-btn')) {
              console.log('DOCK栏刷新按钮被点击！');
              
              sketchup.refresh_docks();
            }
          });
        }
    };
    window.oneToolbarAPI = window.oneToolbarAPI || {};

    /**
     * 批量处理从Ruby发送过来的命令列表，为每个命令生成并保存图标。
     * @param {Array} commands - 包含命令对象的数组。每个对象应包含 unique_id, icon_data_uri, tooltip。
     * @param {string} sourceToolbarName - 这些命令所属的源工具栏名称。
     */
     window.oneToolbarAPI.batchGenerateAndSaveIcons = function(commands, sourceToolbarName) {
       
       // 使用Promise.all来处理所有异步操作，让它们可以并行执行以提高效率
       const promises = commands.map(command => {
           // 只处理类型为'command'且有图标数据(icon_data_uri)的项目
           if (command.type !== 'command' || !command.icon_data_uri) {
               return Promise.resolve(); // 对于非命令或无图标的，返回一个立即成功的Promise，跳过它
           }
           
           // 为每个需要处理的图标创建一个Promise
           return new Promise((resolve, reject) => {
               const img = new Image();
               
               // 当图片成功加载后执行
               img.onload = function() {
                   try {
                       // 1. 创建一个 96x96 的虚拟画布 (Canvas)
                       const canvas = document.createElement('canvas');
                       canvas.width = 96;
                       canvas.height = 96;
                       const ctx = canvas.getContext('2d');
       
                       // ===================================================================
                       // ▼▼▼【核心修正】▼▼▼
                       // 放弃复杂的按比例缩放逻辑，
                       // 直接使用和拖拽方式完全相同的“拉伸填充”逻辑。
                       ctx.drawImage(img, 0, 0, 96, 96);
                       // ===================================================================
       
                       // 3. 从Canvas获取PNG格式的Base64数据
                       const base64Png = canvas.toDataURL('image/png');
       
                       // 4. 清理数据头("data:image/png;base64,"), 得到纯净的Base64字符串
                       const pureBase64 = base64Png.split(',')[1];
       
                       // 5. 调用Ruby的回调函数，将生成好的图标数据发回后端保存
                       sketchup.save_generated_folder_icon(command.unique_id, sourceToolbarName, pureBase64);
                       
                       resolve(); // 通知Promise，当前这个图标处理成功了
                   } catch (e) {
                       console.error(`处理图标'${command.tooltip}'时Canvas操作失败:`, e);
                       reject(e); // 处理失败
                   }
               };
               
               // 当图片加载失败时执行
               img.onerror = function() {
                   console.error(`无法加载图标资源: ${command.tooltip} (URI: ${command.icon_data_uri})`);
                   reject(new Error('Image load error')); // 加载失败
               };
       
               // 启动图片加载
               img.src = command.icon_data_uri;
           });
       });
       
       // 当所有图标的Promise都完成后执行 (这部分逻辑不变)
       Promise.all(promises)
           .then(() => {
               const successMsg = `'${sourceToolbarName}' 的所有图标已预缓存完成！`;
               console.log(successMsg);
               sketchup.show_status_feedback(successMsg);
           })
           .catch(error => {
               const errorMsg = `预缓存 '${sourceToolbarName}' 的图标时发生错误。`;
               console.error(errorMsg, error);
               sketchup.show_status_feedback(errorMsg, true);
           });
     };

     function promptAndSetCenterIcon(diskId) {
       pendingDiskIdForIcon = diskId;
       sketchup.select_image_file(); // 调用Ruby打开文件选择框
     }
 
     // 【新增】当Ruby返回选择的文件路径后，此函数会被调用
     function onCenterIconFileSelected(dataUri) {
      if (!dataUri || !pendingDiskIdForIcon) {
        pendingDiskIdForIcon = null; // 如果没有有效数据，重置并退出
        return;
      }
      
      // 1. 创建一个Image对象来加载Ruby传来的图片数据
      const img = new Image();
      img.onload = function() {
        try {
          // 2. 图片加载成功后，创建96x96的画布
          const canvas = document.createElement('canvas');
          canvas.width = 96;
          canvas.height = 96;
          const ctx = canvas.getContext('2d');
          
          // 3. 将图片绘制到画布上，这个过程会强制缩放/拉伸到96x96
          ctx.drawImage(img, 0, 0, 96, 96);
          
          // 4. 从画布中提取处理后的、纯净的Base64数据
          const resizedBase64Data = canvas.toDataURL('image/png').split(',')[1];
          
          // 5. 将最终处理好的数据发回给Ruby保存
          sketchup.set_disk_center_icon(pendingDiskIdForIcon, resizedBase64Data);

        } catch (e) {
            alert('处理图片时发生错误：' + e.message);
        } finally {
            pendingDiskIdForIcon = null; // 无论成功失败，都重置
        }
      };

      img.onerror = function() {
          alert('无法加载所选图片，请确保文件未损坏并重试。');
          pendingDiskIdForIcon = null;
      }
      
      // 启动图片加载
      img.src = dataUri;
    }
  </script>
    <div id="context-menu" style="display: none; position: absolute; z-index: 1000;">
    </div>

    <!-- 右下角垂直拉伸手柄 -->
    <div class="resize-handle" title="拖拽调整高度"></div>
</body>
</html>