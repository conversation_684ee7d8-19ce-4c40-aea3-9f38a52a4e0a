# dock_overlay.rb

if Sketchup.version.to_i >= 23
  module OneToolbarCreator
    class DockOverlay < Sketchup::Overlay
      attr_reader :item_regions, :switch_button_region
      # 【新增代码】允许外部读取新按钮的区域信息
      attr_reader :auto_hide_button_region

      def initialize(dock_data, all_docks_in_slot, slot_name, start_expanded: false, show_control_button: false, auto_hide_behavior: true)
        super("com.one_studio.one_toolbar.dock.#{slot_name}", "ONE DOCK: #{slot_name.capitalize}")
        @dock_data = dock_data
        @all_docks_in_slot = all_docks_in_slot
        @slot_name = slot_name
        @textures = {}
        @item_regions = []
        @last_mouse_pos = Geom::Point3d.new(-1, -1, 0)
        @hovered_item_id = nil
        @switch_button_region = nil
        @is_switch_hovered = false
        
        # 【新增代码】初始化新按钮相关的实例变量
        @auto_hide_button_region = nil
        @auto_hide_button_center = nil
        @is_auto_hide_hovered = false
        
        # 【核心修改】根据传入参数设置状态
        @is_control_button_visible = show_control_button # 控制按钮是否显示
        @auto_hide_enabled = auto_hide_behavior          # 控制DOCK实际行为
        @is_expanded = !@auto_hide_enabled || start_expanded
        
        @hit_test_region = nil
        @trigger_region = nil
        @expanded_region = nil
        @expanded_region_with_buffer = nil
        @bg_bounds = nil
        @active_parent_id = nil 
        @hide_timer = nil
        @tooltip_text = nil
        @tooltip_position = nil
        @hover_timer = nil
        @current_hover_id = nil
      end

      # 【修改】一个公共方法，用于从外部更新DOCK的行为状态
      def update_auto_hide_behavior(new_behavior)
        @auto_hide_enabled = new_behavior
        
        # 如果关闭了自动隐藏，则强制展开
        @is_expanded = true unless @auto_hide_enabled
  
        # --- 【核心修正】 ---
        # 当关闭自动隐藏时，必须检查并取消任何可能正在运行的隐藏定时器
        if !@auto_hide_enabled && @hide_timer
          UI.stop_timer(@hide_timer)
          @hide_timer = nil
        end
        # --- 【修正结束】 ---
        
        # 立即重绘以反映按钮图标的变化
        Sketchup.active_model.active_view.invalidate if valid?
      end

      # 【修改】onLButtonDown 现在调用新的、只切换行为的方法
      def onLButtonDown(flags, x, y, view)
        click_point = Geom::Point3d.new(x, y, 0)
        
        if @switch_button_region && @switch_button_region.contains?(click_point)
          OneToolbarCreator.cycle_dock_for_slot(@slot_name)
          return true
        end

        if @auto_hide_button_region && @auto_hide_button_region.contains?(click_point)
          OneToolbarCreator.toggle_auto_hide_behavior(@slot_name)
          return true
        end

        return false
      end

      # ... start, stop, get_bounds 等方法保持不变 ...
      def start
        load_textures(Sketchup.active_model.active_view)
      end

      def stop
        pop_ephemeral_tool
        release_textures(Sketchup.active_model.active_view)
      end

      def get_bounds
        return nil if @item_regions.empty?
        bounds = Geom::BoundingBox.new
        @item_regions.each { |r| bounds.add(r[:rect]) }
        return bounds
      end
      
      def onMouseMove(flags, x, y, view)
        active_tool = Sketchup.active_model.tools.active_tool
        folder_tool_is_active = active_tool.is_a?(OneToolbarCreator::FolderDisplayTool)
      
        if folder_tool_is_active && active_tool.parent_slot_name != @slot_name
        elsif folder_tool_is_active && active_tool.parent_slot_name == @slot_name
          @last_mouse_pos = Geom::Point3d.new(x, y, 0)
          # 【修改代码】在 handle_hover_logic 中处理新按钮的悬停
          hover_has_changed = handle_hover_logic(flags)
          view.invalidate if hover_has_changed
          return
        end
      
        @last_mouse_pos = Geom::Point3d.new(x, y, 0)
  
        hover_has_changed = false
        if @is_expanded
            hover_has_changed = handle_hover_logic(flags)
        end
  
        is_over_trigger = @trigger_region&.contains?(@last_mouse_pos)
        is_over_expanded_area = @is_expanded && @combined_hot_zone&.contains?(@last_mouse_pos)
        is_mouse_over_tool = is_over_trigger || is_over_expanded_area
  
        if is_mouse_over_tool
            if @hide_timer
                UI.stop_timer(@hide_timer)
                @hide_timer = nil
            end
            
            needs_redraw_for_state_change = !@is_expanded
            @is_expanded = true
            
            view.invalidate if needs_redraw_for_state_change || hover_has_changed
        else
            if @auto_hide_enabled && @is_expanded && @hide_timer.nil?
                @hide_timer = UI.start_timer(3, false) do
                    @is_expanded = false
                    @hide_timer = nil
                    pop_ephemeral_tool
                    Sketchup.active_model.active_view.invalidate
                end
            end
            
            view.invalidate if hover_has_changed
        end
      end
      
      def handle_hover_logic(flags)
        # 【修改代码】在变量中也追踪新按钮的悬停状态
        previous_hover_id = @hovered_item_id; previous_switch_hover = @is_switch_hovered; previous_auto_hide_hover = @is_auto_hide_hovered

        detect_hover(Sketchup.active_model.active_view)
        
        hover_has_changed = (@hovered_item_id != previous_hover_id || @is_switch_hovered != previous_switch_hover || @is_auto_hide_hovered != previous_auto_hide_hover)
        
        if hover_has_changed
          pop_ephemeral_tool
          active_tool = Sketchup.active_model.tools.active_tool
          unless (flags & MK_LBUTTON) != 0 || active_tool.is_a?(OneToolbarCreator::FolderDisplayTool)
            if @is_switch_hovered
              Sketchup.active_model.tools.push_tool(ClickableDockSwitchTool.new(@slot_name, @switch_button_region))
            # 【新增代码】当悬停在新按钮上时，推入新的临时工具
            elsif @is_auto_hide_hovered
              Sketchup.active_model.tools.push_tool(ClickableDockAutoHideTool.new(@slot_name, @auto_hide_button_region))
            elsif @hovered_item_id
              hovered_region = @item_regions.find { |r| r[:id] == @hovered_item_id }
              if hovered_region && (item_data = hovered_region[:item])
                case item_data[:type]
                when 'command'; push_ephemeral_tool(item_data[:unique_id])
                when 'folder';
                  # 【修改代码】将新按钮的区域信息也传递给文件夹工具
                  tool = OneToolbarCreator::ClickableFolderLauncherTool.new(
                    item_data, hovered_region[:center], self.item_regions, 
                    @switch_button_region, @auto_hide_button_region, @slot_name
                  )
                  Sketchup.active_model.tools.push_tool(tool)
                end
              end
            end
          end
        end
        return hover_has_changed
      end
      
      def draw(view)
        active_tool = Sketchup.active_model.tools.active_tool
        folder_tool_is_active_for_this_dock = active_tool.is_a?(OneToolbarCreator::FolderDisplayTool) && active_tool.parent_slot_name == @slot_name
        @active_parent_id = folder_tool_is_active_for_this_dock ? active_tool.parent_folder_id : nil
        if folder_tool_is_active_for_this_dock
          UI.stop_timer(@hide_timer) if @hide_timer; @hide_timer = nil
          @is_expanded = true
        end
        precompute_layout(view)
        @is_expanded ? draw_expanded_view(view) : draw_collapsed_view(view)

        OneToolbarCreator.draw_custom_tooltip_globally(view, @tooltip_text, @last_mouse_pos)
      end
      
      private

      def precompute_layout(view)
        # 清空所有旧的计算数据
        @item_regions.clear
        @switch_button_region = nil; @switch_button_center = nil
        @auto_hide_button_region = nil; @auto_hide_button_center = nil

        # 图标和DOCK栏尺寸计算 (这部分保持不变)
        icon_size = 36; bg_padding = 10; item_gap = 14;
        if @slot_name == 'right'
            bar_content_height = 0
            if @dock_data[:commands].any?
                commands_count = @dock_data[:commands].count { |c| c[:type] != 'separator' }; separator_count = @dock_data[:commands].count { |c| c[:type] == 'separator' }; bar_content_height = (commands_count * icon_size) + (separator_count * (item_gap / 2.0)) + ([@dock_data[:commands].length - 1, 0].max * item_gap) + (bg_padding * 2)
            end; bar_content_height = [bar_content_height, icon_size + bg_padding * 2].max; bar_x = view.vpwidth - (icon_size + bg_padding * 2) - 15; bar_y = (view.vpheight - bar_content_height) / 2.0; current_y = bar_y + bg_padding;
            (@dock_data[:commands] || []).each { |item| item_x = bar_x + bg_padding; item_id = item[:unique_id] || item[:id]; item_height = (item[:type] == 'separator') ? (item_gap / 2.0) : icon_size; @item_regions << { id: item_id, item: item, center: Geom::Point3d.new(item_x + icon_size / 2.0, current_y + item_height / 2.0, 0), rect: Geom::BoundingBox.new.add(Geom::Point3d.new(item_x, current_y, 0), Geom::Point3d.new(item_x + icon_size, current_y + item_height, 0))}; current_y += item_height + item_gap }
        else
            bar_content_width = 0
            if @dock_data[:commands].any?
                commands_count = @dock_data[:commands].count { |c| c[:type] != 'separator' }; separator_count = @dock_data[:commands].count { |c| c[:type] == 'separator' }; bar_content_width = (commands_count * icon_size) + (separator_count * (item_gap / 2.0)) + ([@dock_data[:commands].length - 1, 0].max * item_gap) + (bg_padding * 2)
            end; bar_content_width = [bar_content_width, icon_size + bg_padding * 2].max; bar_x = (view.vpwidth - bar_content_width) / 2.0; bar_y = (@slot_name == 'top') ? 15 : view.vpheight - (icon_size + bg_padding * 2) - 15; current_x = bar_x + bg_padding;
            (@dock_data[:commands] || []).each { |item| item_y = bar_y + bg_padding; item_id = item[:unique_id] || item[:id]; item_width = (item[:type] == 'separator') ? (item_gap / 2.0) : icon_size; @item_regions << {id: item_id, item: item, center: Geom::Point3d.new(current_x + item_width / 2.0, item_y + icon_size / 2.0, 0), rect: Geom::BoundingBox.new.add(Geom::Point3d.new(current_x, item_y, 0), Geom::Point3d.new(current_x + item_width, item_y + icon_size, 0))}; current_x += item_width + item_gap }
        end
        
        if !@item_regions.empty?
            size = 22.0; half_size = size / 2.0; button_gap = 26.0
            first_item_rect = @item_regions.first[:rect]
            last_item_rect = @item_regions.last[:rect]
            bg_bounds_for_buttons = Geom::BoundingBox.new.add(first_item_rect.min, last_item_rect.max)
            
            pos_A = nil # “主位”
            pos_B = nil # “次位”

            # --- 【核心修正：根据您的定义，重新计算 pos_A 和 pos_B】 ---
            case @slot_name
            when 'top'
                # 顶部DOCK：主位A在“上”
                base_x = bg_bounds_for_buttons.max.x + 25.0
                base_y = bg_bounds_for_buttons.center.y
                pos_A = Geom::Point3d.new(base_x, base_y - (button_gap / 2.0), 0)
                pos_B = Geom::Point3d.new(base_x, base_y + (button_gap / 2.0), 0)
            when 'bottom'
                # 底部DOCK：主位A在“下”
                base_x = bg_bounds_for_buttons.max.x + 25.0
                base_y = bg_bounds_for_buttons.center.y
                top_pos = Geom::Point3d.new(base_x, base_y - (button_gap / 2.0), 0)
                bottom_pos = Geom::Point3d.new(base_x, base_y + (button_gap / 2.0), 0)
                pos_A = bottom_pos # 主位是下方位置
                pos_B = top_pos    # 次位是上方位置
            when 'right'
                # 右侧DOCK：主位A在“右”
                base_x = bg_bounds_for_buttons.center.x
                base_y = bg_bounds_for_buttons.max.y + 25.0
                left_pos = Geom::Point3d.new(base_x - (button_gap / 2.0), base_y, 0)
                right_pos = Geom::Point3d.new(base_x + (button_gap / 2.0), base_y, 0)
                pos_A = right_pos # 主位是右方位置
                pos_B = left_pos  # 次位是左方位置
            end

            # 按钮分配逻辑（这部分无需改动，因为它依赖于上面修正后的 pos_A 和 pos_B）
            is_switch_visible = @all_docks_in_slot.length > 1
            is_hide_visible = @is_control_button_visible

            if is_hide_visible && is_switch_visible
              if @slot_name == 'top'
                @auto_hide_button_center = pos_A
                @switch_button_center = pos_B
              else 
                @auto_hide_button_center = pos_A
                @switch_button_center = pos_B
              end
            elsif is_hide_visible
              @auto_hide_button_center = pos_A
              @switch_button_center = nil
            elsif is_switch_visible
              @switch_button_center = pos_A
              @auto_hide_button_center = nil
            end
            # --- 【修正结束】 ---
            
            if @switch_button_center; @switch_button_region = Geom::BoundingBox.new.add(Geom::Point3d.new(@switch_button_center.x - half_size, @switch_button_center.y - half_size, 0), Geom::Point3d.new(@switch_button_center.x + half_size, @switch_button_center.y + half_size, 0)); end
            if @auto_hide_button_center; @auto_hide_button_region = Geom::BoundingBox.new.add(Geom::Point3d.new(@auto_hide_button_center.x - half_size, @auto_hide_button_center.y - half_size, 0), Geom::Point3d.new(@auto_hide_button_center.x + half_size, @auto_hide_button_center.y + half_size, 0)); end
        end
        
        # 后续的 @bg_bounds, @trigger_region, @combined_hot_zone 计算逻辑保持不变
        if @item_regions.empty?
            @bg_bounds = nil
        else
            first_item_rect = @item_regions.first[:rect]; last_item_rect = @item_regions.last[:rect];
            @bg_bounds = Geom::BoundingBox.new.add(Geom::Point3d.new(first_item_rect.min.x - bg_padding, first_item_rect.min.y - bg_padding, 0), Geom::Point3d.new(last_item_rect.max.x + bg_padding, last_item_rect.max.y + bg_padding, 0))
        end

        return if @bg_bounds.nil?
        vp_width = view.vpwidth; vp_height = view.vpheight;
        case @slot_name
            when 'bottom'; trigger_height = 5; trigger_width = @bg_bounds.width; start_x = @bg_bounds.center.x - (trigger_width / 2.0); @trigger_region = Geom::BoundingBox.new.add(Geom::Point3d.new(start_x, vp_height - trigger_height, 0), Geom::Point3d.new(start_x + trigger_width, vp_height, 0))
            when 'top'; trigger_height = 5; trigger_width = @bg_bounds.width; start_x = @bg_bounds.center.x - (trigger_width / 2.0); @trigger_region = Geom::BoundingBox.new.add(Geom::Point3d.new(start_x, 0, 0), Geom::Point3d.new(start_x + trigger_width, trigger_height, 0))
            when 'right'; trigger_width_v = 5; trigger_height_v = @bg_bounds.height; start_y = @bg_bounds.center.y - (trigger_height_v / 2.0); @trigger_region = Geom::BoundingBox.new.add(Geom::Point3d.new(vp_width - trigger_width_v, start_y, 0), Geom::Point3d.new(vp_width, start_y + trigger_height_v, 0))
        end

        @combined_hot_zone = Geom::BoundingBox.new
        @combined_hot_zone.add(@bg_bounds) if @bg_bounds
        @combined_hot_zone.add(@trigger_region) if @trigger_region
        active_tool = Sketchup.active_model.tools.active_tool
        if active_tool.is_a?(OneToolbarCreator::FolderDisplayTool) && active_tool.parent_slot_name == @slot_name
            folder_bounds = active_tool.get_bounds
            @combined_hot_zone.add(folder_bounds) if folder_bounds
        end
        buffer = 20; @combined_hot_zone.min.x -= buffer; @combined_hot_zone.min.y -= buffer; @combined_hot_zone.max.x += buffer; @combined_hot_zone.max.y += buffer;
      end

      def draw_expanded_view(view)
        draw_background(view)
        draw_items(view)
        draw_switch_button(view)
        # 【新增代码】调用新按钮的绘制方法
        draw_auto_hide_button(view)
      end

      # ... (其他方法如 draw_collapsed_view, push/pop_ephemeral_tool, load/release_textures 等保持不变) ...
      def draw_collapsed_view(view)
        return unless @trigger_region; is_hovered = @trigger_region.contains?(@last_mouse_pos); color = is_hovered ? [0, 0, 0, 50] : [0, 0, 0, 25]; view.drawing_color = color;
        points = [@trigger_region.min, Geom::Point3d.new(@trigger_region.max.x, @trigger_region.min.y, 0), @trigger_region.max, Geom::Point3d.new(@trigger_region.min.x, @trigger_region.max.y, 0)];
        view.draw2d(GL_POLYGON, points)
      end

      def push_ephemeral_tool(unique_id); active_tool = Sketchup.active_model.tools.active_tool; return if active_tool.is_a?(ClickableDockTool) || active_tool.is_a?(OneToolbarCreator::ClickableFolderLauncherTool); tool = ClickableDockTool.new(unique_id); Sketchup.active_model.tools.push_tool(tool); end
      def pop_ephemeral_tool; active_tool = Sketchup.active_model.tools.active_tool; if active_tool.is_a?(ClickableDockTool) || active_tool.is_a?(OneToolbarCreator::ClickableFolderLauncherTool) || active_tool.is_a?(ClickableDockSwitchTool) || active_tool.is_a?(ClickableDockAutoHideTool); Sketchup.active_model.tools.pop_tool; end; end
      def load_textures(view); release_textures(view); (@dock_data[:commands] || []).each do |item|; next if item[:type] == 'separator' || item[:icon_path].nil? || item[:icon_path].empty?; item_id = item[:unique_id] || item[:id]; next if @textures.key?(item_id); absolute_path = File.join(OneToolbarCreator::DATA_DIR, item[:icon_path]); if File.exist?(absolute_path); begin; @textures[item_id] = view.load_texture(Sketchup::ImageRep.new(absolute_path)); rescue => e; @textures[item_id] = nil; end; else; @textures[item_id] = nil; end; end; end
      def release_textures(view); @textures.each_value { |id| view.release_texture(id) if id }; @textures.clear; end

      def detect_hover(view)
        hovered_region = nil
        if @switch_button_region && @switch_button_region.contains?(@last_mouse_pos)
          hovered_region = { type: :switch, id: :switch }
        # 【新增代码】检查对新按钮的悬停
        elsif @auto_hide_button_region && @auto_hide_button_region.contains?(@last_mouse_pos)
          hovered_region = { type: :auto_hide, id: :auto_hide }
        else
          found_icon_region = @item_regions.find { |r| r[:item][:type] != 'separator' && r[:rect].contains?(@last_mouse_pos) }
          hovered_region = found_icon_region if found_icon_region
        end
        
        new_hover_id = hovered_region ? hovered_region[:id] : nil

        @is_switch_hovered = (new_hover_id == :switch)
        # 【新增代码】立即更新新按钮的高亮状态
        @is_auto_hide_hovered = (new_hover_id == :auto_hide)
        @hovered_item_id = (hovered_region && hovered_region[:type] != :switch && hovered_region[:type] != :auto_hide) ? new_hover_id : nil
        
        if new_hover_id != @current_hover_id
          UI.stop_timer(@hover_timer) if @hover_timer
          @hover_timer = nil
          @tooltip_text = nil
          @current_hover_id = new_hover_id
          
          if @current_hover_id 
            @hover_timer = UI.start_timer(1, false) do
              current_pos_region_check = nil
              if @switch_button_region&.contains?(@last_mouse_pos)
                  current_pos_region_check = {id: :switch}
              elsif @auto_hide_button_region&.contains?(@last_mouse_pos)
                  current_pos_region_check = {id: :auto_hide}
              else
                  current_pos_region_check = @item_regions.find { |r| r[:rect].contains?(@last_mouse_pos) }
              end
              return unless current_pos_region_check && current_pos_region_check[:id] == @current_hover_id

              case @current_hover_id
              when :switch
                if @all_docks_in_slot.length > 1
                  current_dock_id = @dock_data[:id]; current_index = @all_docks_in_slot.find_index { |d| d[:id] == current_dock_id }; next_index = (current_index.to_i + 1) % @all_docks_in_slot.length; @tooltip_text = "切换到: #{@all_docks_in_slot[next_index][:name]}"
                else
                  @tooltip_text = "切换DOCK"
                end
              # 【新增代码】为新按钮设置提示文本
              when :auto_hide
                @tooltip_text = @auto_hide_enabled ? "关闭自动隐藏" : "开启自动隐藏"
              else
                final_region = @item_regions.find { |r| r[:id] == @current_hover_id }
                @tooltip_text = final_region[:item][:tooltip] if final_region
              end
              
              view.invalidate
            end
          end
        end
      end
      
      def draw_background(view); return if @bg_bounds.nil?; corner_radius = 8; bg_points = OneToolbarCreator.get_rounded_rect_points(@bg_bounds, corner_radius); view.drawing_color = [0, 0, 0, 30]; view.draw2d(GL_POLYGON, bg_points); end
      def draw_switch_button(view); return unless @switch_button_center; size = 20; half_size = size / 2.0; normal_color = [0, 0, 0, 15]; hover_color = [200, 225, 255, 180]; bg_color = @is_switch_hovered ? hover_color : normal_color; view.drawing_color = bg_color; view.draw2d(GL_POLYGON, OneToolbarCreator.get_circle_points(@switch_button_center, half_size, 24)); text_options = { font: 'Arial', size: 20, bold: true, color: [100, 100, 100, 255] }; icon_symbol = "⇋"; horizontal_nudge = 0; vertical_nudge = -2; text_bounds = view.text_bounds(Geom::Point3d.new(0,0,0), icon_symbol, **text_options); text_x = @switch_button_center.x - (text_bounds.width / 2.0) + horizontal_nudge; text_y = @switch_button_center.y - (text_bounds.height / 2.0) + vertical_nudge; text_point = Geom::Point3d.new(text_x, text_y, 0); view.draw_text(text_point, icon_symbol, text_options); end
      
      # 【新增代码】绘制新按钮的方法
      def draw_auto_hide_button(view)
        return unless @auto_hide_button_center
        
        size = 20.0; half_size = size / 2.0;
        normal_color = [0, 0, 0, 15]
        hover_color = [200, 225, 255, 180]
        bg_color = @is_auto_hide_hovered ? hover_color : normal_color
        
        # 绘制圆形背景
        view.drawing_color = bg_color
        view.draw2d(GL_POLYGON, OneToolbarCreator.get_circle_points(@auto_hide_button_center, half_size, 24))

        # --- 【核心修改】 ---

        # 根据自动隐藏状态，选择不同的字符
        icon_symbol = @auto_hide_enabled ? "📌" : "─"

        # 1. 定义变量，用于存储每个符号的定制属性
        font_size = 13      # 默认字体大小
        vertical_nudge = 0  # 默认垂直偏移

        # 2. 使用 case 语句，为每个符号独立设置字体大小和垂直偏移
        case icon_symbol
        when "📌"
          font_size = 13      # 为 "📌" 设置一个稍大的字体
          vertical_nudge = -1   # "📌" 向下移动1个像素
        when "─"
          font_size = 14      # "〓" 保持原来的字体大小
          vertical_nudge = -1.5    # "〓" 保持垂直居中
        end

        # 3. 在创建 text_options 时，使用我们刚刚设置好的 font_size 变量
        text_options = { 
          font: 'Arial', 
          size: font_size, # 使用变量来设置大小
          bold: true, 
          color: [100, 100, 100, 255]
        }
        # --- 【修改结束】 ---

        horizontal_nudge = 0
        
        # 计算文字绘制位置
        text_bounds = view.text_bounds(Geom::Point3d.new(0,0,0), icon_symbol, **text_options)
        text_x = @auto_hide_button_center.x - (text_bounds.width / 2.0) + horizontal_nudge
        text_y = @auto_hide_button_center.y - (text_bounds.height / 2.0) + vertical_nudge
        text_point = Geom::Point3d.new(text_x, text_y, 0)
        
        # 绘制字符
        view.draw_text(text_point, icon_symbol, text_options)
      end

      # ... (draw_items 和其他辅助方法保持不变) ...
      def draw_items(view)
        # 您修改这里的值，现在会即时生效
        icon_size = 44
        bg_alpha = 210
        
        @item_regions.each do |region|
          item = region[:item]
          if item[:type] == 'separator'
            view.drawing_color = [255, 255, 255, 50]
            if @slot_name == 'right'
              sep_y = region[:center].y - 1
              sep_x_start = region[:rect].min.x + 4
              sep_x_end = region[:rect].max.x - 4
              view.draw2d(GL_QUADS, [sep_x_start, sep_y, 0], [sep_x_end, sep_y, 0], [sep_x_end, sep_y + 2, 0], [sep_x_start, sep_y + 2, 0])
            else
              sep_x = region[:center].x - 1
              sep_y_start = region[:rect].min.y + 4
              sep_y_end = region[:rect].max.y - 4
              view.draw2d(GL_QUADS, [sep_x, sep_y_start, 0], [sep_x + 2, sep_y_start, 0], [sep_x + 2, sep_y_end, 0], [sep_x, sep_y_end, 0])
            end
            next
          end
          is_active_parent = (@active_parent_id == region[:id])
          is_mouse_hovered = (@hovered_item_id == region[:id])
          is_highlighted = is_active_parent || is_mouse_hovered
          
          view.drawing_color = is_highlighted ? [200, 225, 255, bg_alpha] : [210, 210, 210, bg_alpha]
          
          # --- 【核心修正 1】 ---
          # 将硬编码的 36 / 2.0 替换为 icon_size / 2.0，来绘制背景圆圈
          view.draw2d(GL_POLYGON, get_circle_points(region[:center], icon_size / 2.0, 32))
          
          texture_id = @textures[region[:id]]
          if texture_id
            # --- 【核心修改】 ---
            # 1. 计算图标绘制半径
            icon_draw_radius = (icon_size / 2.0) * 0.85

            # 2. 调用全局辅助方法
            points, uvs = OneToolbarCreator.get_textured_circle_data(region[:center], icon_draw_radius)

            # 3. 使用三角扇模式绘制
            view.drawing_color = "white"
            view.draw2d(GL_TRIANGLE_FAN, points, uvs: uvs, texture: texture_id)
            # --- 【修改结束】 ---
          end
        end
      end
      def get_circle_points(center, radius, num_segments = 36); (0..num_segments).map {|i| angle = 2*Math::PI*i/num_segments; Geom::Point3d.new(center.x+radius*Math.cos(angle), center.y+radius*Math.sin(angle), 0)}; end
    end
  end
end