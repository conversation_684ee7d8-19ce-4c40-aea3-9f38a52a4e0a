<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>大锋插件管理器V3.0</title>
  <style>
    /* ===== 基础设置 ===== */
    :root {
      --bg-color: #ffffff;
      --text-color: #333333;
      --border-color: #e0e0e0;
      --header-bg: #f8f9fa;
      --group-bg: #e8e8e8;
      --group-hover: #d0d0d0;
      --toolbar-bg: #f0f0f0;
      --accent-color: #4a90e2;
      --radius: 8px;
      --shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body, html {
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      background-color: var(--bg-color);
      color: var(--text-color);
      overflow: hidden;
    }

    /* ===== 主容器 ===== */
    .app {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }

    /* ===== 头部 ===== */
    .header {
      background: var(--header-bg);
      padding: 16px 20px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      align-items: center;
      gap: 16px;
      flex-shrink: 0;
    }

    .header-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--accent-color), #357abd);
      border-radius: var(--radius);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 18px;
      box-shadow: var(--shadow);
    }

    .header-info {
      flex: 1;
      min-width: 0;
    }

    .header-info h1 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .header-info p {
      font-size: 12px;
      color: #666;
    }



    /* ===== 搜索栏 ===== */
    .search-bar {
      background: var(--header-bg);
      padding: 12px 20px;
      border-bottom: 1px solid var(--border-color);
    }

    .search-container {
      position: relative;
      max-width: 300px;
    }

    .search-input {
      width: 100%;
      padding: 10px 40px 10px 16px;
      border: 1px solid var(--border-color);
      border-radius: 20px;
      background: white;
      font-size: 14px;
      outline: none;
      transition: all 0.2s ease;
    }

    .search-input:focus {
      border-color: var(--accent-color);
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
    }

    .search-icon {
      position: absolute;
      right: 14px;
      top: 50%;
      transform: translateY(-50%);
      color: #999;
      font-size: 16px;
      pointer-events: none;
    }

    .search-clear {
      position: absolute;
      right: 14px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      color: #999;
      font-size: 18px;
      cursor: pointer;
      padding: 2px;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      display: none;
      align-items: center;
      justify-content: center;
    }

    .search-clear:hover {
      background: #f0f0f0;
    }

    .search-container.has-content .search-clear {
      display: flex;
    }

    .search-container.has-content .search-icon {
      display: none;
    }

    /* ===== 主内容区 ===== */
    .main-content {
      flex: 1;
      overflow-y: auto;
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    .main-content::-webkit-scrollbar {
      display: none;
    }

    /* ===== 分组样式 ===== */
    .group {
      margin-bottom: 1px;
    }

    .group-header {
      /* 使用table-cell方案，最可靠的居中方法 */
      display: table-cell;
      vertical-align: middle;
      text-align: center;

      /* 基础样式 */
      background: var(--group-bg);
      font-weight: 600;
      font-size: 13px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      user-select: none;

      /* 尺寸 */
      height: 40px;
      width: 100%;

      /* 重置 */
      margin: 0;
      padding: 0;
      border: none;
      box-sizing: border-box;
    }

    .group-header:hover {
      background: var(--group-hover);
    }

    /* ===== 工具栏样式 ===== */
    .toolbar {
      border-bottom: 1px solid var(--border-color);
    }

    .toolbar-header {
      background: var(--group-bg);
      padding: 8px 20px;
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .toolbar-header:hover {
      background: var(--group-hover);
    }

    .toolbar-name {
      flex: 1;
    }

    .toolbar-status {
      color: #28a745;
      font-size: 10px;
    }

    .expand-icon {
      font-size: 10px;
      transition: transform 0.2s ease;
    }

    .toolbar.expanded .expand-icon {
      transform: rotate(90deg);
    }

    /* ===== 功能图标区 ===== */
    .toolbar-content {
      background: var(--toolbar-bg);
      padding: 8px 12px; /* 进一步减少padding */
      display: none;
    }

    .toolbar.expanded .toolbar-content {
      display: block;
    }

    .icon-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 4px; /* 减少间距到4px */
      justify-content: flex-start; /* 左对齐 */
      align-items: flex-start;
      width: fit-content; /* 容器宽度适应内容，消除右侧空白 */
      max-width: 100%;
    }

    .icon-item {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0; /* 防止图标被压缩 */
    }

    .icon-item:hover {
      background: rgba(74, 144, 226, 0.1);
      transform: translateY(-1px);
    }

    .icon-item img {
      width: 24px;
      height: 24px;
    }

    /* ===== 暗色模式 ===== */
    body.dark-mode {
      --bg-color: #2d2d2d;
      --text-color: #e0e0e0;
      --border-color: #555;
      --header-bg: #3a3a3a;
      --group-bg: #3a3a3a;
      --group-hover: #4a4a4a;
      --toolbar-bg: #2a2a2a;
    }

    body.dark-mode .search-input {
      background: #3a3a3a;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-mode .search-input:focus {
      border-color: var(--accent-color);
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
    }

    body.dark-mode .search-input::placeholder {
      color: #999;
    }

    body.dark-mode .search-clear:hover {
      background: #555;
    }

    /* ===== 实用功能 ===== */
    .empty-message {
      text-align: center;
      padding: 40px 20px;
      color: #999;
      font-style: italic;
    }

    .toolbar-count {
      font-size: 10px;
      color: #999;
      margin-left: 4px;
    }

    /* ===== 响应式设计 ===== */
    @media (max-width: 400px) {
      .header {
        padding: 12px 16px;
      }

      .search-bar {
        padding: 8px 16px;
      }

      .group-header {
        padding: 10px 16px;
      }

      .toolbar-header {
        padding: 6px 16px;
      }

      .toolbar-content {
        padding: 8px 16px;
      }
    }

    /* ===== 动画效果 ===== */
    .toolbar-content {
      transition: all 0.3s ease;
      overflow: hidden;
    }

    .icon-item {
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .group {
      transition: all 0.2s ease;
    }

    /* ===== 工具提示 ===== */
    .tooltip {
      position: relative;
    }

    .tooltip::after {
      content: attr(data-tooltip);
      position: absolute;
      bottom: 100%;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      opacity: 0;
      pointer-events: none;
      transition: opacity 0.2s ease;
      z-index: 1000;
    }

    .tooltip:hover::after {
      opacity: 1;
    }

    /* ===== 调试样式（可选） ===== */
    .debug .group-header {
      border: 2px solid red;
    }

    .debug .group-title {
      border: 1px solid blue;
      background: rgba(0, 255, 0, 0.1);
    }

    /* ===== 强制居中的备用方案 ===== */
    .group-header-alt {
      background: var(--group-bg);
      height: 40px;
      position: relative;
      cursor: pointer;
      transition: background-color 0.2s ease;
      user-select: none;
    }

    .group-header-alt:hover {
      background: var(--group-hover);
    }

    .group-header-alt .group-title-alt {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-weight: 600;
      font-size: 13px;
      white-space: nowrap;
    }

    /* ===== Grid居中方案 ===== */
    .group-header-grid {
      background: var(--group-bg);
      height: 40px;
      display: grid;
      place-items: center;
      cursor: pointer;
      transition: background-color 0.2s ease;
      user-select: none;
    }

    .group-header-grid:hover {
      background: var(--group-hover);
    }

    .group-header-grid .group-title-grid {
      font-weight: 600;
      font-size: 13px;
      white-space: nowrap;
    }

    /* ===== Table-cell居中方案 ===== */
    .group-header-table {
      background: var(--group-bg);
      height: 40px;
      display: table-cell;
      vertical-align: middle;
      text-align: center;
      width: 100%;
      cursor: pointer;
      transition: background-color 0.2s ease;
      user-select: none;
    }

    .group-header-table:hover {
      background: var(--group-hover);
    }

    .group-header-table .group-title-table {
      font-weight: 600;
      font-size: 13px;
      white-space: nowrap;
      display: inline-block;
    }
  </style>
</head>
<body>
  <div class="app">
    <!-- 头部 -->
    <div class="header">
      <div class="header-icon">大</div>
      <div class="header-info">
        <h1>大锋插件管理器V3.0</h1>
        <p><EMAIL></p>
      </div>

    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <div class="search-container">
        <input type="text" class="search-input" placeholder="搜索插件..." 
               oninput="handleSearchInput()" onkeydown="handleSearchKeydown(event)">
        <span class="search-icon">⌕</span>
        <button class="search-clear" onclick="clearSearch()" title="清除搜索">×</button>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content" id="main-content">
      <!-- 内容将通过JavaScript动态生成 -->
    </div>
  </div>

  <script>
    // 搜索功能
    function handleSearchInput() {
      const searchInput = document.querySelector('.search-input');
      const searchContainer = searchInput.parentElement;
      
      if (searchInput.value.trim()) {
        searchContainer.classList.add('has-content');
      } else {
        searchContainer.classList.remove('has-content');
      }
      
      renderContent();
    }

    function handleSearchKeydown(event) {
      if (event.key === 'Escape') {
        clearSearch();
      }
    }

    function clearSearch() {
      const searchInput = document.querySelector('.search-input');
      const searchContainer = searchInput.parentElement;
      
      searchInput.value = '';
      searchContainer.classList.remove('has-content');
      searchInput.focus();
      
      renderContent();
    }

    // 切换工具栏展开/收起
    function toggleToolbar(element) {
      const toolbar = element.closest('.toolbar');
      toolbar.classList.toggle('expanded');
    }

    // 主题切换
    function toggleTheme() {
      const body = document.body;
      const themeButton = document.querySelector('.theme-toggle');

      body.classList.toggle('dark-mode');

      if (body.classList.contains('dark-mode')) {
        themeButton.textContent = '☀️';
        themeButton.setAttribute('data-tooltip', '切换到浅色模式');
        localStorage.setItem('theme', 'dark');
      } else {
        themeButton.textContent = '🌙';
        themeButton.setAttribute('data-tooltip', '切换到暗色模式');
        localStorage.setItem('theme', 'light');
      }
    }

    // 加载保存的主题
    function loadTheme() {
      const savedTheme = localStorage.getItem('theme');
      const themeButton = document.querySelector('.theme-toggle');

      if (savedTheme === 'dark') {
        document.body.classList.add('dark-mode');
        themeButton.textContent = '☀️';
        themeButton.setAttribute('data-tooltip', '切换到浅色模式');
      }
    }

    // 图标点击事件
    function onIconClick(iconName) {
      console.log('点击了图标:', iconName);
      // 这里可以添加实际的功能调用
    }

    // 切换居中方案
    const centerMethods = ['flex', 'absolute', 'grid', 'table'];
    let currentMethodIndex = 0;

    function toggleCenterMethod() {
      currentMethodIndex = (currentMethodIndex + 1) % centerMethods.length;
      const centerMethod = centerMethods[currentMethodIndex];
      console.log('切换到居中方案:', centerMethod);

      // 更新按钮提示
      const button = document.querySelector('.center-toggle');
      button.setAttribute('data-tooltip', `当前: ${centerMethod} 居中`);

      renderContent();
    }

    function getCurrentCenterMethod() {
      return centerMethods[currentMethodIndex];
    }

    // 切换图标布局
    const iconLayouts = ['auto-grid', 'precise-grid', 'flex'];
    let currentLayoutIndex = 0;

    function toggleIconLayout() {
      currentLayoutIndex = (currentLayoutIndex + 1) % iconLayouts.length;
      const layout = iconLayouts[currentLayoutIndex];
      console.log('切换到图标布局:', layout);

      // 更新按钮提示
      const button = document.querySelector('.layout-toggle');
      button.setAttribute('data-tooltip', `当前: ${layout} 布局`);

      renderContent();
    }

    function getCurrentIconLayout() {
      return iconLayouts[currentLayoutIndex];
    }

    // 示例数据
    const sampleData = {
      groups: [
        {
          name: "默认分组",
          toolbars: [
            {
              name: "综合类大插件集",
              status: "●",
              expanded: true,
              icons: [
                { name: "工具1", icon: "🔧" },
                { name: "工具2", icon: "⚙️" },
                { name: "工具3", icon: "🔨" },
                { name: "工具4", icon: "📐" },
                { name: "工具5", icon: "📏" }
              ]
            },
            {
              name: "辅助的相关插件",
              status: "●",
              expanded: false,
              icons: [
                { name: "辅助1", icon: "🎯" },
                { name: "辅助2", icon: "🎨" },
                { name: "辅助3", icon: "🖼️" }
              ]
            },
            {
              name: "组织组件编辑类",
              status: "●",
              expanded: false,
              icons: [
                { name: "编辑1", icon: "✏️" },
                { name: "编辑2", icon: "📝" },
                { name: "编辑3", icon: "🗂️" }
              ]
            }
          ]
        },
        {
          name: "智达云",
          toolbars: [
            {
              name: "云端工具",
              status: "●",
              expanded: false,
              icons: [
                { name: "云工具1", icon: "☁️" },
                { name: "云工具2", icon: "🌐" }
              ]
            }
          ]
        }
      ]
    };

    // 渲染内容
    function renderContent() {
      const searchTerm = document.querySelector('.search-input').value.toLowerCase();
      const mainContent = document.getElementById('main-content');

      let html = '';

      sampleData.groups.forEach(group => {
        // 过滤工具栏
        const filteredToolbars = group.toolbars.filter(toolbar => {
          if (!searchTerm) return true;
          return toolbar.name.toLowerCase().includes(searchTerm) ||
                 toolbar.icons.some(icon => icon.name.toLowerCase().includes(searchTerm));
        });

        if (filteredToolbars.length === 0) return;

        html += `<div class="group">`;
        html += `<div class="group-header">${group.name}</div>`;

        filteredToolbars.forEach(toolbar => {
          html += `<div class="toolbar ${toolbar.expanded ? 'expanded' : ''}">`;
          html += `<div class="toolbar-header" onclick="toggleToolbar(this)">`;
          html += `<span class="toolbar-name">${toolbar.name}</span>`;
          html += `<span class="toolbar-count">(${toolbar.icons.length})</span>`;
          html += `<span class="toolbar-status">${toolbar.status}</span>`;
          html += `<span class="expand-icon">▶</span>`;
          html += `</div>`;
          html += `<div class="toolbar-content">`;
          html += `<div class="icon-grid">`;

          toolbar.icons.forEach(icon => {
            if (!searchTerm || icon.name.toLowerCase().includes(searchTerm)) {
              html += `<div class="icon-item tooltip" data-tooltip="${icon.name}" onclick="onIconClick('${icon.name}')">`;
              html += `<span style="font-size: 20px;">${icon.icon}</span>`;
              html += `</div>`;
            }
          });

          html += `</div></div></div>`;
        });

        html += `</div>`;
      });

      if (html === '') {
        html = '<div class="empty-message">没有找到匹配的插件</div>';
      }

      mainContent.innerHTML = html;
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      loadTheme();
      renderContent();
    });
  </script>
</body>
</html>
