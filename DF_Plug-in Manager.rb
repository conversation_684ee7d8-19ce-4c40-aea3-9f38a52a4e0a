require 'sketchup.rb'
require 'extensions.rb'
require 'digest/md5'

module DF_PluginManager

  # 设置正确的文件路径
  plugin_dir = File.dirname(__FILE__)
  toolbar_creator_path = File.join(plugin_dir, "DF_Plug-in Manager", "toolbar_creator.rb")
  loader_path = File.join(plugin_dir, "DF_Plug-in Manager", "toolbar_creator")

  # 检查文件是否存在
  unless File.exist?(toolbar_creator_path)
    puts "Error: toolbar_creator.rb not found at: #{toolbar_creator_path}"
    return
  end

  # 先加载toolbar_creator文件以获取常量
  begin
    load toolbar_creator_path
  rescue LoadError => e
    puts "Error loading toolbar_creator: #{e.message}"
    puts "File path: #{toolbar_creator_path}"
    return
  rescue => e
    puts "Unexpected error loading toolbar_creator: #{e.message}"
    puts "File path: #{toolbar_creator_path}"
    return
  end

  PLUGIN_NAME = "大锋插件管理器V3.0"
  STRING = "大锋插件管理器V3.0.用于创建圆盘菜单,Dock工具栏等快捷方式."

  extension = SketchupExtension.new(OneToolbarCreator::REG_NAME, loader_path)
  extension.creator = "一个工作室"
  extension.version = OneToolbarCreator::VERSION
  extension.copyright = "2024-2025, ONE_Studio."
  extension.description = STRING

  Sketchup.register_extension(extension, true)

end
