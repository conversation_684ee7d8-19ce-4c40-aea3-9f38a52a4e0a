# toolbar_creator.rb (Version 3.0)
lib_dir = File.dirname(__FILE__)
$LOAD_PATH.unshift(lib_dir) unless $LOAD_PATH.include?(lib_dir)

Sketchup.require 'base64'
Sketchup.require 'sketchup.rb'
Sketchup.require 'json'
Sketchup.require 'fileutils'
Sketchup.require 'securerandom'
Sketchup.require 'set'
Sketchup.require 'quick_toolbar_tool'
Sketchup.require 'folder_popup'
Sketchup.require 'tmpdir'

# --- 【核心修改】进行版本检测 ---
if Sketchup.version.to_i >= 23
  Sketchup.require 'dock_overlay'
end

# --- 【新增代码：创建AppObserver类】 ---
class OneToolbarAppObserver < Sketchup::AppObserver
  def onNewModel(model)
    OneToolbarCreator.reset_dock_indices # 【新增】
    OneToolbarCreator.show_or_update_docks(model)
  end

  def onOpenModel(model)
    OneToolbarCreator.reset_dock_indices # 【新增】
    OneToolbarCreator.show_or_update_docks(model)
  end
end
# --- 【新增结束】 ---

module OneToolbarCreator
    # 【新增】定义插件的注册名和版本号为一个常量
    REG_NAME = "大锋插件管理器V3.0".freeze
    VERSION = "3.0".freeze
    # 【新增】定义插件的根目录为一个常量
    PLUGIN_DIR = File.dirname(__FILE__).freeze

    # 【新增】定义每个环的槽位数量限制
    RING_LIMITS = [
      { min: 4, max: 8 },   # 内环
      { min: 6, max: 12 },  # 中环
      { min: 8, max: 16 }   # 外环
    ]

    # 定义一个全局的色彩容差常量，用于处理抗锯齿的边缘像素
    ICON_BG_TOLERANCE = 30
  extend self

  PLUGIN_NAME = "大锋插件管理器V3.0"
  DATA_DIR = File.join(File.dirname(__FILE__), 'DF_PluginManager_Data')
  CONFIG_FILE = File.join(DATA_DIR, 'custom_toolbars.json')
  
  @radial_commands = []
  CUSTOM_TOOLBAR_COMMANDS = {}
  @created_toolbars = {}
  @dialog = nil
  @pinned_folder_data = nil # 用于记录当前被固定的文件夹数据
  @dialog_for_js = nil      # 用于后台与JS通信的Dialog实例
  @disk_to_focus = nil # 用于记住需要被聚焦展开的圆盘ID
    # --- 【新增】用于管理菜单的模块变量 ---
  @main_menu = nil               # 存储 "大锋插件管理器" 这个主菜单
  @radial_commands_submenu = nil # 存储 "圆盘菜单" 这个子菜单
  @created_radial_command_names = Set.new # 【新增】使用Set来追踪已创建的菜单项名称，效率高且自动去重
  @dock_display_indices = { 'top' => 0, 'bottom' => 0, 'right' => 0 } # 【新增】记录每个插槽的显示索引

  @live_command_cache = {}
  @is_cache_ready = false

  @dock_overlays = {}

    # 【新增】重置所有插槽的显示索引为0
  def self.reset_dock_indices
    @dock_display_indices = { 'top' => 0, 'bottom' => 0, 'right' => 0 }
  end
  
  # 【新增】为指定插槽循环到下一个Dock栏
  def self.cycle_dock_for_slot(slot_name)
    # --- 【核心新增逻辑】---
    # 在执行任何切换操作之前，先检查并关闭任何已打开的文件夹弹出窗口
    tools = Sketchup.active_model.tools
    while tools.active_tool.is_a?(OneToolbarCreator::FolderDisplayTool) ||
          tools.active_tool.is_a?(OneToolbarCreator::ClickableFolderLauncherTool)
      tools.pop_tool
    end
    # --- 【新增结束】---

    return unless @dock_display_indices.key?(slot_name)

    configs = get_configs
    assigned_ids = configs.dig('dock_bar_assignments', slot_name) || []
    return if assigned_ids.length <= 1

    current_index = @dock_display_indices[slot_name] || 0
    new_index = (current_index + 1) % assigned_ids.length
    
    @dock_display_indices[slot_name] = new_index

    # --- 【核心修改点】 ---
    # 不再传递一个笼统的 start_expanded: true
    # 而是通过一个新的参数 expanded_slot，明确告知更新函数哪一个插槽需要被展开
    show_or_update_docks(Sketchup.active_model, expanded_slot: slot_name)
    # --- 【修改结束】 ---
  end

  def self.cache_all_command_data
    # puts "正在同步缓存所有工具条命令..."
    @live_command_cache.clear
    @is_cache_ready = false
    begin
      ObjectSpace.each_object(UI::Toolbar) do |toolbar|
        next if toolbar.name.empty?
        toolbar.each_with_index do |command, index|
          next unless command.is_a?(UI::Command)
          tooltip_for_id = command.tooltip.empty? ? "NoTooltip_#{command.object_id}" : command.tooltip
          unique_id = "#{toolbar.name}_#{index}_#{tooltip_for_id}"
          @live_command_cache[unique_id] = command
        end
      end
      @is_cache_ready = true
      # puts "命令缓存完成！共缓存 #{@live_command_cache.size} 个有效命令。"
      # 【修复】: 不再从这里调用 refresh_ui()
    rescue => e
      puts "ERROR: 缓存命令时发生错误: #{e.message}\n#{e.backtrace.join("\n")}"
    end
  end

  # 新的、统一的dialog获取器
  def self.get_dialog
    # 如果@dialog实例已存在，则直接返回它 (无论它当前可见或隐藏)
    return @dialog if @dialog
  
    # 如果@dialog实例不存在(比如首次运行或已被关闭)，则创建一个新的
    # 【关键】我们先创建一个隐藏的实例，用于执行后台任务或准备显示
    puts "[OneToolbar] Creating new hidden HtmlDialog instance..."
    options = {
      :dialog_title => "大锋插件管理器V3.0",
      :width => 250,
      :height => 600,
      :min_width => 250,
      :max_width => 250,
      :min_height => 400,
      :max_height => 1200,
      :resizable => true,
      :scrollable => false,
      :style => UI::HtmlDialog::STYLE_DIALOG,
      :visible => false # 默认不可见
    }
    @dialog = UI::HtmlDialog.new(options)
    
    # 为这个新实例设置好所有的回调
    setup_dialog_callbacks(@dialog)
    
    # 设置关闭事件，当窗口被用户关闭时，将@dialog重置为nil
    @dialog.set_on_closed { @dialog = nil }
    
    return @dialog
  end

  def self.setup_dialog_callbacks(dialog)
    html_path = File.join(File.dirname(__FILE__), 'ui', 'dialog.html')
    dialog.set_file(html_path)
  
    @dialog.add_action_callback("get_initial_data") { |_, _|
      OneToolbarCreator.cache_all_command_data
      OneToolbarCreator.refresh_ui
    }

    # 【新增】为直接执行命令功能注册回调
    @dialog.add_action_callback("execute_command_by_id") { |_, unique_id|
      execute_command_by_id(unique_id)
    }
    # 【新增】为导入/导出功能注册回调
    @dialog.add_action_callback("import_settings") { |_, _| import_settings }
    @dialog.add_action_callback("export_settings") { |_, _| export_settings }
    # 【新增】为更新圆盘布局注册回调
    @dialog.add_action_callback("update_disk_layout") { |_, disk_id, ring_counts, qa_count| 
      update_disk_layout(disk_id, ring_counts, qa_count) 
    }
    # 【新增】为设置圆盘起点功能注册回调
    @dialog.add_action_callback("set_disk_start_angle") { |_, disk_id, value| 
      set_disk_start_angle(disk_id, value) 
    }
    @dialog.add_action_callback("move_or_swap_radial_command") { |_, d_id, sr_idx, ss_idx, tr_idx, ts_idx| move_or_swap_radial_command(d_id, sr_idx, ss_idx, tr_idx, ts_idx) } # 【新增】注册圆盘内部移动/交换回调
    # 【新增】为右键增/删槽位注册回调
    @dialog.add_action_callback("insert_radial_slot") { |_, d_id, r_idx, s_idx| insert_radial_slot(d_id, r_idx, s_idx) }
    @dialog.add_action_callback("delete_radial_slot") { |_, d_id, r_idx, s_idx| delete_radial_slot(d_id, r_idx, s_idx) }
  
    # -- Custom Toolbar Callbacks --
    @dialog.add_action_callback("create_toolbar") {function createGroupElement(group)  |_, name| create_or_update_toolbar(name); refresh_ui(false) }
    @dialog.add_action_callback("delete_toolbar") { |_, name| confirm_and_delete_toolbar(name) }
    @dialog.add_action_callback("rename_toolbar") { |_, old_name| prompt_and_rename_toolbar(old_name) }
    @dialog.add_action_callback("delete_toolbar_command") { |_, name, id, tooltip| confirm_and_delete_command(name, id, tooltip) }
    @dialog.add_action_callback("add_or_reorder_command") { |_, name, id, target_id, is_after, base64| add_or_reorder_command(name, id, target_id, is_after, base64); refresh_ui(false) }
    @dialog.add_action_callback("reorder_toolbars") { |_, ordered_names| reorder_toolbars(ordered_names) }
    @dialog.add_action_callback("confirm_and_delete_toolbar") { |_, name| 
      confirm_and_delete_toolbar(name) 
    }
    @dialog.add_action_callback("prompt_and_create_toolbar") { |_, _| prompt_and_create_toolbar }
    @dialog.add_action_callback("update_radial_item_position") { |_, data| update_radial_item_position(data) }
    @dialog.add_action_callback("reorder_disks_in_group") { |_, group_id, ordered_ids| reorder_disks_in_group(group_id, ordered_ids) }
    # -- Radial Menu Callbacks --
    # 【BUG修复】: 修正了这里的回调名称，以匹配JavaScript中的调用
    @dialog.add_action_callback("prompt_and_create_disk") { |_, _| prompt_and_create_disk }
    @dialog.add_action_callback("prompt_and_create_group") { |_, _| prompt_and_create_group }
    
    @dialog.add_action_callback("delete_radial_item") { |_, type, id| delete_radial_item(type, id); refresh_ui(true) }
    @dialog.add_action_callback("rename_radial_item") { |_, type, id, old_name| prompt_and_rename_radial_item(type, id, old_name) }
    @dialog.add_action_callback("set_disk_group") { |_, disk_id, group_id| set_disk_group(disk_id, group_id); refresh_ui(false) }
    @dialog.add_action_callback("save_disk_order") { |_, order| save_disk_order(order); refresh_ui(false) }
    @dialog.add_action_callback("open_shortcut_settings") { |_, disk_name|
      # 1. 构造命令名称
      command_name_to_copy = "圆盘: #{disk_name}"
      
      # 2. 清理字符串中的特殊字符（特别是单引号），以安全地传递给JavaScript
      safe_string = command_name_to_copy.gsub("'", "\\'")
      
      # 3. 构造并执行JavaScript命令，调用一个我们即将在html中创建的新函数
      js_command = "copyTextToClipboard('#{safe_string}')"
      @dialog.execute_script(js_command)
      
      # 4. 最后再打开快捷键窗口
      UI.show_preferences('Shortcuts')
    }
  
    # 【新增】一个辅助回调，用于接收来自JavaScript的成功消息并在状态栏显示
    @dialog.add_action_callback("show_status_feedback") { |_, text|
      Sketchup.status_text = text
      UI.start_timer(5) { Sketchup.status_text = "" } # 5秒后清空
    }

    # 【新增】为右键菜单中的“重命名工具栏”操作注册回调
    @dialog.add_action_callback("prompt_and_rename_toolbar") { |_, old_name| 
      prompt_and_rename_toolbar(old_name) 
    }
    
    # 【新增】为右键菜单中的“重命名圆盘/分组”操作注册回调
    @dialog.add_action_callback("prompt_and_rename_radial_item") { |_, type, id, old_name| 
      prompt_and_rename_radial_item(type, id, old_name) 
    }
    @dialog.add_action_callback("add_command_to_disk") { |_, disk_id, ring_index, slot_index, cmd_id, base64| add_command_to_disk(disk_id, ring_index, slot_index, cmd_id, base64); refresh_ui(false) }
    @dialog.add_action_callback("remove_command_from_disk") { |_, disk_id, ring_index, slot_index| remove_command_from_disk(disk_id, ring_index, slot_index); refresh_ui(false) }
    @dialog.add_action_callback("delete_command") { |_, name, id| 
      delete_command(name, id)
      refresh_ui(false) 
    }
    # 【新增】为快捷动作条功能注册回调
    @dialog.add_action_callback("set_quick_action_command") { |_, d_id, s_idx, cmd_id, b64| set_quick_action_command(d_id, s_idx, cmd_id, b64) }
    @dialog.add_action_callback("set_quick_action_key") { |_, d_id, s_idx, key| set_quick_action_key(d_id, s_idx, key) }
    @dialog.add_action_callback("remove_quick_action") { |_, d_id, s_idx| remove_quick_action(d_id, s_idx) }
    @dialog.add_action_callback("move_or_swap_quick_action") { |_, d_id, src_idx, tgt_idx| move_or_swap_quick_action(d_id, src_idx, tgt_idx) } # 【新增】注册移动/交换回调
    @dialog.add_action_callback("new_disk_created") { |_, disk_id| # 这个回调实际上不需要在 Ruby 中处理，但可以作为一种通知机制
      # Ruby 端实际不需要在这里做什么，而是直接从 create_radial_disk 调用 JS
    }
    @dialog.add_action_callback("prompt_and_create_dock_bar") { |_, _| prompt_and_create_dock_bar }
    @dialog.add_action_callback("add_command_to_dock_bar") { |_, dock_id, cmd_id, target_id, is_after, base64| 
      add_command_to_dock_bar(dock_id, cmd_id, target_id, is_after, base64)
      refresh_ui(false)
    }
    @dialog.add_action_callback("add_separator_to_dock_bar") { |_, dock_id|
      add_separator_to_dock_bar(dock_id)
      refresh_ui(false)
    }
    @dialog.add_action_callback("reorder_dock_item") { |_, dock_id, src_id, tgt_id, is_after|
      reorder_dock_item(dock_id, src_id, tgt_id, is_after)
      refresh_ui(false)
    }
    @dialog.add_action_callback("add_separator_after_item") { |_, dock_id, item_id|
      add_separator_after_item(dock_id, item_id)
      refresh_ui(false)
    }
    @dialog.add_action_callback("delete_command_from_dock_bar") { |_, dock_id, cmd_id|
      delete_command_from_dock_bar(dock_id, cmd_id)
      refresh_ui(false)
    }
    
    @dialog.add_action_callback("prompt_and_rename_dock_bar") { |_, id, name| prompt_and_rename_dock_bar(id, name) }
    @dialog.add_action_callback("confirm_and_delete_dock_bar") { |_, id, name| confirm_and_delete_dock_bar(id, name) }
  
    @dialog.add_action_callback("add_separator_to_toolbar") { |_, name, target_id, is_after|
      add_separator_to_toolbar(name, target_id, is_after)
      refresh_ui(false)
    }
    @dialog.add_action_callback("reorder_toolbar_item") { |_, name, src_id, tgt_id, is_after|
      reorder_toolbar_item(name, src_id, tgt_id, is_after)
      refresh_ui(false)
    }
    @dialog.add_action_callback("add_separator_to_toolbar_end") { |_, name|
      add_separator_to_toolbar_end(name)
      refresh_ui(false)
    }
  
    @dialog.add_action_callback("add_folder_to_dock_bar") { |_, dock_id, source_name, cover_id, data, is_emoji|
      add_folder_to_dock_bar(dock_id, source_name, cover_id, data, is_emoji)
      refresh_ui(false)
    }
  
    @dialog.add_action_callback("delete_item_from_dock_bar") { |_, dock_id, item_id|
      delete_item_from_dock_bar(dock_id, item_id)
      refresh_ui(false)
    }
  
    @dialog.add_action_callback("assign_dock_bar_to_slot") { |_, dock_id, slot_name, target_id, is_after|
      assign_dock_bar_to_slot(dock_id, slot_name, target_id, is_after)
      refresh_ui(false)
    }
    @dialog.add_action_callback("reorder_dock_bar_in_slot") { |_, slot_name, ordered_ids|
      reorder_dock_bar_in_slot(slot_name, ordered_ids)
      refresh_ui(false)
    }
    @dialog.add_action_callback("unassign_dock_bar") { |_, dock_id|
      unassign_dock_bar(dock_id)
      refresh_ui(false)
    }
      # 为“手动刷新DOCK栏”功能注册回调
      @dialog.add_action_callback("refresh_docks") { |_, _| 
      # 直接调用我们现有的、功能强大的DOCK栏更新方法即可
      show_or_update_docks(Sketchup.active_model) 
    }

        # 【新增】为删除分隔符功能注册回调
    @dialog.add_action_callback("delete_separator_from_toolbar") { |_, name, id|
      delete_separator_from_toolbar(name, id)
      refresh_ui(false) # 操作完成后刷新界面
    }
    
    @dialog.add_action_callback("delete_separator_from_dock") { |_, dock_id, sep_id|
      delete_separator_from_dock(dock_id, sep_id)
      refresh_ui(false) # 操作完成后刷新界面
    }

    @dialog.add_action_callback("save_generated_folder_icon") { |_, unique_id, source_name, b64| save_generated_folder_icon(unique_id, source_name, b64) }
    @dialog.add_action_callback("request_folder_icon_redraw") { |_, source_name| precache_folder_icons_async(source_name) }
    @dialog.add_action_callback("confirm_and_redraw_folder_icons") { |_, source_name, tooltip| confirm_and_redraw_folder_icons(source_name, tooltip) }
    @dialog.add_action_callback("move_item_between_docks") { |_, src_dock, tgt_dock, item_id, tgt_item_id, is_after| move_item_between_docks(src_dock, tgt_dock, item_id, tgt_item_id, is_after) }
    @dialog.add_action_callback("move_item_between_custom_toolbars") { |_, src_name, tgt_name, item_id, tgt_item_id, is_after| move_item_between_custom_toolbars(src_name, tgt_name, item_id, tgt_item_id, is_after) }
    @dialog.add_action_callback("set_dock_control_button_visibility") { |_, slot_name, is_visible| set_dock_control_button_visibility(slot_name, is_visible) }

    @dialog.add_action_callback("select_image_file") { |_, _| select_image_file }
    @dialog.add_action_callback("set_disk_center_icon") { |_, disk_id, base64_data| set_disk_center_icon(disk_id, base64_data) }

    # 分组管理回调函数
    @dialog.add_action_callback("create_plugin_group") { |_, group_name| create_plugin_group(group_name) }
    @dialog.add_action_callback("rename_plugin_group") { |_, old_name, new_name| rename_plugin_group(old_name, new_name) }
    @dialog.add_action_callback("delete_plugin_group") { |_, group_name| delete_plugin_group(group_name) }

    # 工具栏管理回调函数
    @dialog.add_action_callback("toggle_toolbar_visibility") { |_, toolbar_name| toggle_toolbar_visibility(toolbar_name) }
    @dialog.add_action_callback("move_toolbar_to_group") { |_, toolbar_name, group_name| move_toolbar_to_group(toolbar_name, group_name) }
    @dialog.add_action_callback("uninstall_plugin") { |_, toolbar_name| uninstall_plugin(toolbar_name) }

    # 显示设置回调函数
    @dialog.add_action_callback("set_separator_display") { |_, show_separators| set_separator_display(show_separators) }

    # 拖拽排序回调函数
    @dialog.add_action_callback("reorder_plugin_groups") { |_, source_group, target_group, is_after| reorder_plugin_groups(source_group, target_group, is_after) }
    @dialog.add_action_callback("reorder_toolbars_in_group") { |_, source_toolbar, target_toolbar, group_name, is_after| reorder_toolbars_in_group(source_toolbar, target_toolbar, group_name, is_after) }
    @dialog.add_action_callback("move_toolbar_between_groups") { |_, toolbar_name, source_group, target_group, target_toolbar, is_after| move_toolbar_between_groups(toolbar_name, source_group, target_group, target_toolbar, is_after) }
    @dialog.add_action_callback("move_toolbar_to_group_position") { |_, toolbar_name, source_group, target_group, is_after| move_toolbar_to_group_position(toolbar_name, source_group, target_group, is_after) }

  end
  
  # 简化后的 show_dialog 方法
  def self.show_dialog
    dialog = get_dialog
    
    # 1. 先捕获对话框在本次调用前的可见状态
    was_visible = dialog.visible?
  
    # 2. 如果不可见，则显示它。然后总是把它带到最前面。
    dialog.show unless was_visible
    dialog.bring_to_front
  
    # 3. 【核心修正】只有当对话框原本就可见时，我们才立即刷新。
    #    如果对话框是新显示的，我们不在这里刷新，而是等待前端
    #    通过 get_initial_data 回调来触发第一次刷新。
    #    这样就完美解决了时序问题。
    if was_visible
      OneToolbarCreator.cache_all_command_data
      OneToolbarCreator.refresh_ui
    end
    # 如果 was_visible 为 false，此方法会安静地结束，
    # 将刷新任务完全交给 get_initial_data 回调。
  end

  # --- 【新增代码】 ---
  def self.show_and_focus_disk(disk_id)
    # 1. 将需要展开的 disk_id 记在我们的实例变量里
    @disk_to_focus = disk_id
  
    # 2. 调用 show_dialog。
    # 如果对话框已打开，show_dialog 内部的 refresh_ui 会立刻处理 @disk_to_focus。
    # 如果对话框是新创建的，@disk_to_focus 的值会一直保留，直到 get_initial_data 回调触发 refresh_ui。
    show_dialog
  end
  # --- 【新增结束】 ---

  # 【最终修正版】发送给前端的数据中，额外包含了SketchUp的版本号
  def self.refresh_ui(reload_commands = false)
    return unless @dialog && @dialog.visible?
    load_and_recreate_radial_commands if reload_commands
  
    configs = get_configs # 【新增】获取一次配置
    source_data = @is_cache_ready ? get_source_command_data : []
    custom_toolbar_data = get_custom_toolbar_data
    radial_data = get_radial_data
    dock_bar_data = get_dock_bar_data
    dock_assignments = configs['dock_bar_assignments'] || { 'top' => [], 'bottom' => [], 'right' => [] }
  
    full_payload = {
      sketchupVersion: Sketchup.version.to_i,
      pluginVersion: VERSION, # 【新增】将插件版本号也发送给前端
      sourceData: source_data,
      customToolbarData: custom_toolbar_data,
      radialData: radial_data,
      dockBarData: dock_bar_data,
      dockAssignments: dock_assignments,
      dockSlotSettings: configs['dock_slot_settings'], # 【新增】将插槽设置添加到载荷中
      pluginGroups: configs['plugin_groups'], # 【新增】将分组配置添加到载荷中
      displaySettings: configs['display_settings'], # 【新增】将显示设置添加到载荷中
      diskToFocus: @disk_to_focus
    }
    
    @disk_to_focus = nil 
    
    js_command = "renderAllData(#{JSON.generate(full_payload)})"
    @dialog.execute_script(js_command)
  end


  def self.get_default_configs
    {
      'custom_toolbars' => [],
      'custom_toolbars_order' => [],
      'radial_disks' => [],
      'radial_groups' => [],
      'radial_disks_order' => [],
      # --- 【新增部分】 ---
      'dock_slot_settings' => {
        'top' => { 'auto_hide' => false },
        'bottom' => { 'auto_hide' => false },
        'right' => { 'auto_hide' => false }
      },
      'plugin_groups' => {
        # 只保留一个空的默认配置，所有插件都会归类到"其他插件"分组
      },
      'display_settings' => {
        'show_separators' => false
      }
    }
  end

  # 【新增】接收并保存前端发来的自动隐藏设置
  def self.set_dock_slot_auto_hide(slot_name, state)
    configs = get_configs
    configs['dock_slot_settings'] ||= {}
    configs['dock_slot_settings'][slot_name] ||= {}
    configs['dock_slot_settings'][slot_name]['auto_hide'] = state
    save_configs(configs)
  end

    # 【新增】根据命令名称查找其快捷键的辅助方法
  def self.get_shortcut_for_command_name(command_name)
    # Sketchup.get_shortcuts 返回一个字符串数组
    all_shortcuts = Sketchup.get_shortcuts

    # 【核心修正】使用 .each 来遍历数组，而不是 .each_line
    all_shortcuts.each do |line|
      # chomp用于移除行尾可能的回车换行符
      parts = line.chomp.split("\t")
      next unless parts.length == 2
      
      shortcut_key = parts[0]
      command_path = parts[1]
      
      # 我们检查命令全路径是否以我们的目标命令名称结尾
      # 这样可以精确匹配，避免 "命令A" 匹配到 "某个菜单/命令A"
      if command_path.end_with?(command_name)
        return shortcut_key # 找到匹配，立即返回快捷键
      end
    end
    
    return nil # 遍历完成仍未找到，返回nil
  end

  def self.get_configs
    if File.exist?(CONFIG_FILE)
      JSON.parse(File.read(CONFIG_FILE, encoding: "UTF-8"))
    else
      get_default_configs
    end
  rescue JSON::ParserError => e
    UI.messagebox("警告：配置文件损坏，已重置。\n错误: #{e.message}")
    get_default_configs
  end

  def self.save_configs(configs)
    FileUtils.mkdir_p(DATA_DIR)
    File.open(CONFIG_FILE, "w:UTF-8") { |f| f.write(JSON.pretty_generate(configs)) }
  end

  # 【最终、最优化版】仅在macOS上对PDF图标进行转换，以实现最佳兼容性和性能
  def self.get_source_command_data
    return [] unless @is_cache_ready
    configs = get_configs
    custom_toolbar_names = (configs['custom_toolbars'] || []).map { |c| c['name'] }.to_set
    
    grouped_commands = {}
    
    ObjectSpace.each_object(UI::Toolbar) do |toolbar|
      toolbar_name = toolbar.name.force_encoding("UTF-8")
      next if toolbar_name.empty? || custom_toolbar_names.include?(toolbar_name)

      grouped_commands[toolbar_name] ||= []

      toolbar.each_with_index do |item, index|
        if item.is_a?(UI::Command)
          command = item
          tooltip_for_id = command.tooltip.empty? ? "NoTooltip_#{command.object_id}" : command.tooltip
          unique_id = "#{toolbar.name}_#{index}_#{tooltip_for_id}"
          
          icon_data_uri = nil
          original_icon_path = command.large_icon.empty? ? command.small_icon : command.large_icon

          # --- 【核心修改：条件判断逻辑】 ---
          begin
            # 只有在获取到图标路径，并且该图标是 PDF 格式时，才执行特殊转换
            if original_icon_path && File.extname(original_icon_path).downcase == '.pdf'
              
              # 【优化】只在第一次需要时，才确保缓存目录存在
              pdf_cache_dir = File.join(DATA_DIR, 'pdf_icon_cache')
              FileUtils.mkdir_p(pdf_cache_dir)
              
              # 1. 为这个PDF图标生成一个对应的PNG缓存路径
              cached_png_filename = "#{Digest::MD5.hexdigest(unique_id)}.png"
              cached_png_path_abs = File.join(pdf_cache_dir, cached_png_filename)

              # 2. 如果PNG缓存不存在，则调用 write_bitmap 生成一个96x96的高清版本
              unless File.exist?(cached_png_path_abs)
                command.write_bitmap(cached_png_path_abs, 96, 96)
              end
              
              # 3. 使用这个高清PNG缓存文件来生成 Data URI
              if File.exist?(cached_png_path_abs)
                image_data = File.binread(cached_png_path_abs)
                base64_string = Base64.strict_encode64(image_data)
                icon_data_uri = "data:image/png;base64,#{base64_string}"
              end

            # 对于所有其他情况 (PNG, SVG, 或无图标)，沿用原有的直接读取逻辑
            elsif original_icon_path && File.exist?(original_icon_path)
              image_data = File.binread(original_icon_path)
              base64_string = Base64.strict_encode64(image_data)
              ext = File.extname(original_icon_path).downcase
              mime_type = case ext
                          when ".svg" then "image/svg+xml"
                          when ".png" then "image/png"
                          else "application/octet-stream"
                          end
              icon_data_uri = "data:#{mime_type};base64,#{base64_string}"
            end
          rescue => e
            puts "Error processing icon for command '#{command.tooltip}': #{e.message}"
          end
          # --- 【修改结束】 ---
          
          grouped_commands[toolbar_name] << {
            type: 'command',
            unique_id: unique_id,
            tooltip: command.tooltip.force_encoding("UTF-8"),
            icon_data_uri: icon_data_uri,
            icon_path: "" # 此字段对于原生工具不再重要
          }

        elsif item == "|"
          grouped_commands[toolbar_name] << { type: 'separator', id: "#{toolbar.name}_separator_#{index}" }
        end
      end
    end
    
    # 添加工具栏可见性状态信息
    grouped_commands.map do |name, commands|
      toolbar_obj = nil
      ObjectSpace.each_object(UI::Toolbar) do |tb|
        if tb.name.force_encoding("UTF-8") == name
          toolbar_obj = tb
          break
        end
      end

      visible = toolbar_obj ? toolbar_obj.visible? : true
      { name: name, commands: commands, visible: visible }
    end.sort_by { |t| t[:name] }
  end

  def self.get_custom_toolbar_data
    configs = get_configs
    toolbars = configs['custom_toolbars'] || []
    order = configs['custom_toolbars_order'] || []
  
    toolbar_map = toolbars.map { |tb| [tb['name'], tb] }.to_h
    sorted_toolbars = order.map { |name| toolbar_map[name] }.compact
    remaining_toolbars = toolbars.reject { |tb| order.include?(tb['name']) }
    final_toolbars = sorted_toolbars + remaining_toolbars
  
    final_toolbars.map do |config|
      commands = (config['commands'] || []).map do |cmd_data|
        # 如果旧数据没有type，我们默认为'command'
        type = cmd_data['type'] || 'command' 
  
        # --- 【核心修正】确保所有条目都有type，并根据type处理 ---
        if type == 'command'
          icon_data_uri = nil
          if cmd_data['icon_path'] && !cmd_data['icon_path'].empty?
              absolute_icon_path = File.join(DATA_DIR, cmd_data['icon_path'])
              if File.exist?(absolute_icon_path)
                  image_data = File.binread(absolute_icon_path)
                  base64_string = Base64.strict_encode64(image_data)
                  icon_data_uri = "data:image/png;base64,#{base64_string}"
              end
          end
          { 
            type: 'command', # 明确返回type
            unique_id: cmd_data['unique_id'], 
            tooltip: cmd_data['tooltip'], 
            icon_data_uri: icon_data_uri
          }
        elsif type == 'separator'
          {
            type: 'separator',
            id: cmd_data['id']
          }
        end
        # --- 修正结束 ---
      end.compact
      
      { name: config['name'], commands: commands }
    end
  end

  def self.get_dock_bar_data
    configs = get_configs
    dock_bars = configs['dock_bars'] || []
  
    dock_bars.map do |config|
      commands = (config['commands'] || []).map do |item_data|
        next unless item_data && item_data['type']
        type = item_data['type']
  
        if type == 'command'
          { 
            type: 'command',
            unique_id: item_data['unique_id'], 
            tooltip: item_data['tooltip'], 
            icon_data_uri: self.generate_icon_uri(item_data['icon_path']),
            icon_path: item_data['icon_path'] # 【核心修正】补上这一行
          }
        elsif type == 'separator'
          {
            type: 'separator',
            id: item_data['id']
          }
        elsif type == 'folder'
          { 
            type: 'folder',
            id: item_data['id'],
            unique_id: item_data['id'],
            source_toolbar_name: item_data['source_toolbar_name'],
            tooltip: item_data['tooltip'], 
            emoji: item_data['icon_emoji'],
            icon_data_uri: item_data['icon_emoji'] ? nil : self.generate_icon_uri(item_data['icon_path']),
            icon_path: item_data['icon_path'] # 【核心修正】补上这一行
          }
        end
      end.compact
      
      { name: config['name'], id: config['id'], commands: commands }
    end
  end

  # 【最终重构版】确保只有未被分组的圆盘和分组本身会出现在顶层
  def self.get_radial_data
    configs = get_configs
    disks = configs['radial_disks'] || []
    groups = configs['radial_groups'] || []
    order = configs['radial_disks_order'] || [] 

    grouped_disk_ids = Set.new(groups.flat_map { |g| g['disk_ids'] || [] })

    top_level_disks = disks.reject { |d| grouped_disk_ids.include?(d['id']) }
    
    all_top_level_items = top_level_disks + groups
    item_map = all_top_level_items.map { |item| [item['id'], item] }.to_h

    sorted_items = order.map { |id| item_map[id] }.compact
    
    # ▼▼▼ THIS IS THE CORRECTED LINE ▼▼▼
    sorted_item_ids = sorted_items.map { |item| item['id'] }
    
    remaining_items = all_top_level_items.reject { |item| sorted_item_ids.include?(item['id']) }
    final_ordered_items = sorted_items + remaining_items

    disk_map_full = disks.map { |d| [d['id'], d] }.to_h
    final_ordered_items.each do |item|
      items_to_process = item['disk_ids'] ? (item['disk_ids'] || []).map { |id| disk_map_full[id] }.compact : [item]
      
      items_to_process.each do |disk|
        next unless disk && disk['rings'] # 安全检查
        
        # 【新增】处理中心图标
        if disk['center_icon_path']
          disk['center_icon_data_uri'] = generate_icon_uri(disk['center_icon_path'])
        end
        
        # (处理环形和快捷区图标的逻辑保持不变)
        all_commands = (disk['rings'] || []).flatten.compact + (disk['quick_actions'] || []).compact
        all_commands.each do |cmd_data|
            cmd_data['icon_data_uri'] = generate_icon_uri(cmd_data['icon_path']) if cmd_data && cmd_data['icon_path']
        end
        command_name = "圆盘: #{disk['name']}"
        disk['shortcut'] = get_shortcut_for_command_name(command_name)
      end
      item['disks'] = (item['disk_ids'] || []).map { |id| disk_map_full[id] }.compact if item['disk_ids']
    end

    { allItems: final_ordered_items }
  end

  def self.prompt_and_create_disk
    result = UI.inputbox(["请输入新圆盘的名称:"], ["新的圆盘"], "创建新圆盘")
    if result && result[0] && !(name = result[0].strip).empty?
  
      configs = get_configs
      existing_names = (configs['radial_disks'] || []).map { |d| d['name'] }
  
      # 使用新的辅助方法来获取一个唯一的名称
      unique_name = generate_unique_name(name, existing_names)
  
      # 使用这个保证唯一的名称来创建圆盘
      create_radial_disk(unique_name)
      # 此处不再需要调用 refresh_ui，因为 create_radial_disk 内部的流程已经处理了UI刷新
    end
  end

  def self.prompt_and_create_group
    result = UI.inputbox(["请输入新分组的名称:"], ["新的分组"], "创建新分组")
    if result && result[0] && !(name = result[0].strip).empty?
      create_radial_group(name)
      refresh_ui(false)
    end
  end

  # 【最终修正版】统一通过 show_and_focus_disk 来处理后续的UI交互
  def self.create_radial_disk(name)
    configs = get_configs
    configs['radial_disks'] ||= []
    
    default_slots = [4, 6, 8]
    new_disk = {
      'id' => SecureRandom.uuid, 'name' => name,
      'rings' => [ Array.new(default_slots[0], nil), Array.new(default_slots[1], nil), Array.new(default_slots[2], nil) ],
      'slot_counts' => default_slots, 'quick_actions' => [],
      'quick_action_slot_count' => 6, 'start_at_top' => false
    }
    
    configs['radial_disks'] << new_disk
    configs['radial_disks_order'] ||= []
    configs['radial_disks_order'] << new_disk['id']
    save_configs(configs)

    # 菜单项创建逻辑不变
    return unless @radial_commands_submenu
    disk_id = new_disk['id']
    command_name = "圆盘: #{name}"
    cmd = UI::Command.new(command_name) do
      tools = Sketchup.active_model.tools
      (tools.active_tool.is_a?(QuickToolbarTool)) ? tools.pop_tool : tools.push_tool(QuickToolbarTool.new(disk_id))
    end
    cmd.tooltip = "呼出圆盘菜单 '#{name}'"
    @radial_commands << cmd
    @radial_commands_submenu.add_item(cmd)
    @created_radial_command_names.add(command_name)

    # 【核心修改】不再直接调用refresh_ui或前端的JS函数，
    # 而是统一调用包含完整逻辑的 show_and_focus_disk 方法。
    show_and_focus_disk(new_disk['id'])
  end
  \
  def self.create_radial_group(name)
    configs = get_configs
    configs['radial_groups'] ||= []
    new_group = {
      'id' => SecureRandom.uuid,
      'name' => name,
      'disk_ids' => []
    }
    configs['radial_groups'] << new_group
    save_configs(configs)
  end

  def self.set_disk_start_angle(disk_id, start_at_top)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    if disk
      disk['start_at_top'] = start_at_top
      save_configs(configs)
    end
  end
  # 【新增】将命令设置到快捷动作条的插槽中
  def self.set_quick_action_command(disk_id, slot_index, command_id, base64_data)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk

    # 替换为新的、只检查快捷启动区的检测逻辑:
    is_duplicate_in_qa = (disk['quick_actions'] || []).compact.any? { |cmd| cmd['unique_id'] == command_id }
    if is_duplicate_in_qa
      UI.messagebox("此命令已存在于当前圆盘的快捷启动条中。", MB_OK)
      refresh_ui(false) 
      return
    end

    original_cmd = @live_command_cache[command_id]
    return unless original_cmd
    
    # --- 图标处理逻辑 ---
    icons_dir = File.join(DATA_DIR, 'icons')
    FileUtils.mkdir_p(icons_dir)
    unique_filename = "#{Digest::MD5.hexdigest(command_id)}.png"
    dest_abs = File.join(icons_dir, unique_filename)
    dest_rel = File.join('icons', unique_filename)
    
    if !File.exist?(dest_abs)
      image_binary_data = nil
      if base64_data && !base64_data.empty?
        image_binary_data = Base64.decode64(base64_data)
      else
        src_path = original_cmd.large_icon.empty? ? original_cmd.small_icon : original_cmd.large_icon
        if src_path && File.exist?(src_path)
          image_binary_data = File.binread(src_path)
        end
      end

      # 只要成功获取了图像数据，就调用我们的透明化方法进行处理并保存
      if image_binary_data
        make_icon_background_transparent_and_save(image_binary_data, dest_abs)
      end
    end

    # --- 数据更新逻辑 ---
    disk['quick_actions'] ||= []
    
    old_command = disk['quick_actions'][slot_index]
    check_and_delete_icon(old_command['icon_path'], configs) if old_command && old_command['icon_path']

    new_command_data = {
      'type' => 'command', # 【新增】明确类型为普通命令
      'unique_id' => command_id,
      'tooltip' => original_cmd.tooltip.force_encoding("UTF-8"),
      'icon_path' => dest_rel,
      'key' => old_command ? old_command['key'] : nil 
    }
    disk['quick_actions'][slot_index] = new_command_data
    
    save_configs(configs)
    refresh_ui(false)
  end
  
  # 【新增】为快捷动作条的命令设置快捷键
  def self.set_quick_action_key(disk_id, slot_index, key)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk && disk['quick_actions'] && disk['quick_actions'][slot_index]
  
    # 清理并验证快捷键（只接受单个字母或数字）
    valid_key = key.to_s.strip.upcase[0]
    unless valid_key && valid_key.match?(/^[A-Z0-9]$/)
      valid_key = nil # 如果输入无效，则清空快捷键
    end
  
    # 检查快捷键是否在该圆盘的快捷动作条中已存在
    is_duplicate = (disk['quick_actions'] || []).each_with_index.any? do |action, index|
      action && action['key'] == valid_key && index != slot_index
    end
  
    if is_duplicate
      UI.messagebox("设置失败：快捷键 '#{valid_key}' 在此圆盘的快捷动作条中已被使用。", MB_OK, "快捷键冲突")
      # 【重要】刷新UI以将输入框恢复原状
      refresh_ui(false) 
      return
    end
  
    disk['quick_actions'][slot_index]['key'] = valid_key
    save_configs(configs)
    # 此处无需刷新UI，避免输入框失去焦点
  end
  
  # 【新增】清空快捷动作条的一个插槽
  def self.remove_quick_action(disk_id, slot_index)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk && disk['quick_actions'] && disk['quick_actions'][slot_index]
  
    command_to_remove = disk['quick_actions'][slot_index]
    disk['quick_actions'][slot_index] = nil
    
    save_configs(configs)
    check_and_delete_icon(command_to_remove['icon_path'], get_configs) if command_to_remove
    
    refresh_ui(false)
  end
  
  # 【新增】处理快捷动作条内部命令的移动或交换
  def self.move_or_swap_quick_action(disk_id, source_slot_index, target_slot_index)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk && disk['quick_actions']

    quick_actions = disk['quick_actions']
    source_command = quick_actions[source_slot_index]
    target_command = quick_actions[target_slot_index]

    if source_command.nil?
      # 如果源槽位是空的，但用户尝试拖动（这不应该发生，但作为防御性编程）
      puts "警告: 尝试移动一个空的快捷动作条槽位。"
      return
    end

    # 【核心逻辑】执行交换操作
    quick_actions[target_slot_index] = source_command # 将源命令放到目标位置
    quick_actions[source_slot_index] = target_command # 将目标命令放到源位置

    save_configs(configs)
    refresh_ui(false) # 刷新UI以反映变化
    @dialog.execute_script("showStatusFeedback('快捷动作已移动。')")
  end

  def self.delete_radial_item(type, id)
    configs = get_configs
    item_type_name_cn = type == 'disk' ? '圆盘' : '分组'
    item_name = ""
    restart_needed = false

    if type == 'disk'
      item_to_delete = (configs['radial_disks'] || []).find { |d| d['id'] == id }
      item_name = item_to_delete['name'] if item_to_delete
      restart_needed = true
    else # type == 'group'
      item_to_delete = (configs['radial_groups'] || []).find { |g| g['id'] == id }
      item_name = item_to_delete['name'] if item_to_delete
    end
    return unless item_name

    message = "确定要永久删除#{item_type_name_cn} \"#{item_name}\" 吗？"
    message += "\n\n(此操作无法撤销，且需要重启SketchUp以更新快捷键列表)" if restart_needed
    
    if UI.messagebox(message, MB_YESNO, "确认删除") == IDYES
      if type == 'disk'
        disk_to_delete = (configs['radial_disks'] || []).find { |d| d['id'] == id }
        if disk_to_delete
          # ==================================================================================
          # ▼▼▼ 核心修改：将被删除圆盘的所有关联图标（命令图标+中心图标）合并到待检查列表 ▼▼▼
          # ==================================================================================
          commands_to_check = (disk_to_delete['rings'] || []).flatten.compact + 
                              (disk_to_delete['quick_actions'] || []).compact
          
          # 【新增】获取中心图标的路径，以备后续清理
          center_icon_path_to_check = disk_to_delete['center_icon_path']
          # ======================== ▲▲▲ 核心修改结束 ▲▲▲ ========================
          
          # 从配置中移除该圆盘
          (configs['radial_groups'] || []).each { |g| g['disk_ids']&.delete(id) }
          (configs['radial_disks_order'] || []).delete(id)
          (configs['radial_disks'] || []).reject! { |d| d['id'] == id }
          
          # 【新增】首先，检查并清理中心图标文件
          # 我们把这个检查放在移除圆盘配置之后，这样 check_and_delete_icon 方法
          # 在扫描所有配置时就不会再找到这个图标的引用了。
          check_and_delete_icon(center_icon_path_to_check, configs) if center_icon_path_to_check

          # 然后，按原计划检查并清理所有命令的图标
          commands_to_check.each do |cmd|
            check_and_delete_icon(cmd['icon_path'], configs) if cmd && cmd['icon_path']
          end
        end
      elsif type == 'group'
        (configs['radial_groups'] || []).reject! { |g| g['id'] == id }
      end
      
      save_configs(configs)
      refresh_ui(true)
    end
  end
  
  def self.prompt_and_rename_radial_item(type, id, old_name)
    item_type_name = type == 'disk' ? '圆盘' : '分组'
    result = UI.inputbox(["请输入新的#{item_type_name}名称:"], [old_name], "重命名 #{item_type_name}")
    
    if result && result[0] && !(new_name = result[0].strip).empty? && new_name != old_name
      configs = get_configs
      
      # --- 【核心新增】重命名时的重复名称检测 ---
      if type == 'disk'
        # 获取除自己以外的所有其他圆盘的名称
        other_disk_names = (configs['radial_disks'] || []).reject { |d| d['id'] == id }.map { |d| d['name'].downcase }
        if other_disk_names.include?(new_name.downcase)
          UI.messagebox("重命名失败：已存在名为 \"#{new_name}\" 的圆盘。", MB_OK)
          return # 阻止重命名
        end
      end
      # --- 检测逻辑结束 ---
      
      key = type == 'disk' ? 'radial_disks' : 'radial_groups'
      item = (configs[key] || []).find { |i| i['id'] == id }
      
      if item
        item['name'] = new_name
        save_configs(configs)
        
        # 当重命名圆盘后，我们需要刷新菜单，以便新的命令项可以被创建
        if type == 'disk'
          # 注意：旧的菜单项会保留直到重启，这是我们为避免API崩溃而做的妥协。
          # 但新的菜单项会被正确创建，以便用户可以立刻为其设置快捷键。
          load_and_recreate_radial_commands 
        end
        
        refresh_ui(false) # 刷新对话框UI
        # UI.messagebox("操作成功！请重启SketchUp以更新快捷键命令名称。") if type == 'disk'
      end
    end
  end

  def self.set_disk_group(disk_id, group_id)
    configs = get_configs
    (configs['radial_groups'] || []).each { |g| g['disk_ids']&.delete(disk_id) }
    if group_id != 'none'
      target_group = (configs['radial_groups'] || []).find { |g| g['id'] == group_id }
      target_group['disk_ids'] << disk_id if target_group
    end
    save_configs(configs)
  end

  def self.save_disk_order(order_array)
    configs = get_configs
    configs['radial_disks_order'] = order_array
    save_configs(configs)
  end
  
  def self.add_command_to_disk(disk_id, ring_index, slot_index, command_id, base64_data) # 注意这里参数名是 base64_data
    configs = get_configs
    disks = configs['radial_disks'] || []
    
    # 查找目标圆盘
    target_disk = disks.find { |d| d['id'] == disk_id }
    unless target_disk
      puts "错误: 未找到 ID 为 #{disk_id} 的圆盘。"
      @dialog.execute_script("showStatusFeedback('错误: 未找到目标圆盘。')")
      return
    end

    # 替换为新的、只检查环形区的检测逻辑:
    is_duplicate_in_rings = (target_disk['rings'] || []).flatten.compact.any? { |cmd| cmd['unique_id'] == command_id }
    if is_duplicate_in_rings
      UI.messagebox("此命令已存在于当前圆盘的环形区域中。", MB_OK)
      @dialog.execute_script("showStatusFeedback('此命令已存在于环形区。')")
      return # 阻止添加操作
    end

    # 确保环和槽位结构存在
    target_disk['rings'] ||= []
    while target_disk['rings'].length <= ring_index
      target_disk['rings'] << [] # 添加新的环
    end
    
    current_ring = target_disk['rings'][ring_index]
    current_ring ||= [] # 确保当前环不是nil

    # 检查当前环的槽位数量是否已达到上限
    ring_limit = RING_LIMITS[ring_index]
    if ring_limit && current_ring.compact.length >= ring_limit[:max]
      UI.messagebox("当前环的槽位已满（最多 #{ring_limit[:max]} 个）。请先删除一些命令。", MB_OK)
      @dialog.execute_script("showStatusFeedback('当前环的槽位已满。')")
      return # 阻止添加
    end

    # --- 【核心修复】这里是上次修改导致bug的地方，现在修正图标保存逻辑 ---
    icons_dir = File.join(DATA_DIR, 'icons') # 确保icons目录的存在
    FileUtils.mkdir_p(icons_dir) # 创建目录，如果它不存在
    unique_filename = "#{Digest::MD5.hexdigest(command_id)}.png" # 使用命令ID的MD5哈希作为唯一文件名
    destination_path_absolute = File.join(icons_dir, unique_filename)
    destination_path_relative = File.join('icons', unique_filename) # 存储相对路径

    # 只有当文件不存在时才写入，避免重复写入
    if !File.exist?(destination_path_absolute)
      image_binary_data = nil
      if base64_data && !base64_data.empty?
        image_binary_data = Base64.decode64(base64_data)
      else
        original_cmd = @live_command_cache[command_id]
        source_icon_path = original_cmd.large_icon.empty? ? original_cmd.small_icon : original_cmd.large_icon
        if source_icon_path && File.exist?(source_icon_path)
          image_binary_data = File.binread(source_icon_path)
        else
          UI.messagebox("警告: 命令 '#{command_id}' 没有有效的图标文件可供复制。");
          @dialog.execute_script("showStatusFeedback('警告: 未找到图标文件。')")
        end
      end

      # 只要成功获取了图像数据，就调用我们的透明化方法进行处理并保存
      if image_binary_data
        make_icon_background_transparent_and_save(image_binary_data, destination_path_absolute)
      end
    end
    # --- 【核心修复】结束 ---

    # 查找原始命令的tooltip
    original_command = @live_command_cache[command_id]
    tooltip = original_command ? original_command.tooltip : "未知命令"

    # 构建要添加到槽位的数据
    command_data = {
      'unique_id' => command_id,
      'tooltip' => tooltip,
      'icon_path' => destination_path_relative # 存储相对路径
    }

    # 检查并清理旧命令的图标，如果当前槽位已有命令被替换
    old_command = current_ring[slot_index]
    check_and_delete_icon(old_command['icon_path'], configs) if old_command && old_command['icon_path']

    # 插入或替换命令
    if slot_index < current_ring.length
      current_ring[slot_index] = command_data
    else
      # 如果槽位索引超出当前环的长度，则填充 nil 直到目标槽位
      # 注意：此逻辑需要确保 ring_index 对应的数组长度是正确的，否则会出错
      # 最好在 update_disk_layout 中处理好 rings 数组的长度
      while current_ring.length < slot_index
        current_ring << nil
      end
      current_ring << command_data
    end
    
    save_configs(configs)
    @dialog.execute_script("showStatusFeedback('命令已添加到圆盘。')")
    refresh_ui(false) # 刷新UI以显示更改
  end
  
  def self.remove_command_from_disk(disk_id, ring_index, slot_index)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk && disk['rings'] && disk['rings'][ring_index]

    command_to_remove = disk['rings'][ring_index][slot_index]
    disk['rings'][ring_index][slot_index] = nil
    
    check_and_delete_icon(command_to_remove['icon_path'], configs) if command_to_remove
    save_configs(configs)
  end
  
  def self.load_and_recreate_radial_commands
    return unless @radial_commands_submenu 

    configs = get_configs
    (configs['radial_disks'] || []).each do |disk|
      command_name = "圆盘: #{disk['name']}"
      
      # --- 【核心修复】通过检查“名称”是否已在Set中，来决定是否跳过 ---
      next if @created_radial_command_names.include?(command_name)
      # ----------------------------------------------------------------

      disk_id = disk['id']
      cmd = UI::Command.new(command_name) do
        tools = Sketchup.active_model.tools
        (tools.active_tool.is_a?(QuickToolbarTool)) ? tools.pop_tool : tools.push_tool(QuickToolbarTool.new(disk_id))
      end
      cmd.tooltip = "圆盘菜单 '#{disk['name']}'"
      
      @radial_commands << cmd 
      @radial_commands_submenu.add_item(cmd)
      
      # 将新创建的命令名称记录到Set中
      @created_radial_command_names.add(command_name)
    end
  end

  def self.get_live_command(unique_id); @live_command_cache[unique_id]; end
  class AppShutdownObserver < Sketchup::AppObserver; def onQuit; end; end
  
  def self.create_or_update_toolbar(name)
    configs = get_configs
    normalized_name = name.strip
    return if normalized_name.empty?
  
    # 这里的重名检测逻辑已被移除，因为它现在由调用者 prompt_and_create_toolbar 来保证
  
    configs['custom_toolbars'] ||= []
    configs['custom_toolbars'] << { 'name' => normalized_name, 'commands' => [] }
    save_configs(configs)
  end

  def self.delete_toolbar(toolbar_name)
    # --- 【核心新增】开始 ---
    # 1. 从我们的内存中找到要被删除的工具条对象并移除追踪
    toolbar_to_hide = @created_toolbars.delete(toolbar_name)
    # --- 核心新增结束 ---

    configs = get_configs
    toolbar_config = (configs['custom_toolbars'] || []).find { |c| c['name'] == toolbar_name }
    return unless toolbar_config

    commands_to_check = toolbar_config['commands'].dup
    configs['custom_toolbars'].reject! { |c| c['name'] == toolbar_name }
    
    commands_to_check.each do |cmd_data|
      if cmd_data && cmd_data['icon_path']
        check_and_delete_icon(cmd_data['icon_path'], configs)
      end
    end

    save_configs(configs)
    refresh_ui(false)

    # 2. 【核心新增】隐藏工具条对象，让它在本会话中不再可见
    toolbar_to_hide.hide if toolbar_to_hide
  end

  def self.prompt_and_create_toolbar
    result = UI.inputbox(["请输入新工具栏的名称:"], ["新的工具栏"], "创建新工具栏")
    if result && result[0] && !(name = result[0].strip).empty?
  
      configs = get_configs
      existing_names = (configs['custom_toolbars'] || []).map { |c| c['name'] }
  
      # 同样使用辅助方法来获取唯一名称
      unique_name = generate_unique_name(name, existing_names)
  
      create_or_update_toolbar(unique_name) 
      refresh_ui(false) 
    end
  end

  def self.confirm_and_delete_toolbar(toolbar_name)
    # 【核心修改】将所有提示信息整合到这一个确认框内
    message = "确定要永久删除工具栏 \"#{toolbar_name}\" 吗？\n\n(此操作无法撤销，相关设置将在下次启动SketchUp时完全移除)"
    
    if UI.messagebox(message, MB_YESNO, "确认删除工具栏") == IDYES
      delete_toolbar(toolbar_name)
    end
  end

  def self.delete_command(toolbar_name, command_id)
    configs = get_configs
    toolbar_config = (configs['custom_toolbars'] || []).find { |c| c['name'] == toolbar_name }
    return unless toolbar_config
    command_to_delete = toolbar_config['commands'].find { |cmd| cmd['unique_id'] == command_id }
    toolbar_config['commands'].reject! { |cmd| cmd['unique_id'] == command_id }
    check_and_delete_icon(command_to_delete['icon_path'], configs) if command_to_delete && command_to_delete['icon_path']
    save_configs(configs)
  end

  def self.confirm_and_delete_command(toolbar_name, command_id, tooltip)
    if UI.messagebox("确定要从 \"#{toolbar_name}\" 中删除命令 \"#{tooltip}\" 吗？", MB_YESNO) == IDYES
      delete_command(toolbar_name, command_id)
      refresh_ui(false)
    end
  end
  
  # 【BUG修复】: 补全这个被遗漏的关键辅助方法
  def self.check_and_delete_icon(icon_path_to_check, current_configs)
    return unless icon_path_to_check && !icon_path_to_check.empty?
    
    is_in_use = false
    
    # 1. 检查所有自定义工具栏
    (current_configs['custom_toolbars'] || []).each do |tb|
      if (tb['commands'] || []).any? { |cmd| cmd && cmd['icon_path'] == icon_path_to_check }
        is_in_use = true; break
      end
    end
    return if is_in_use # 如果已找到，提前返回，提高效率
  
    # 2. 检查所有圆盘 (包括环形区和快捷启动区)
    (current_configs['radial_disks'] || []).each do |disk|
      # 检查环形区
      if (disk['rings'] || []).flatten.compact.any? { |cmd| cmd['icon_path'] == icon_path_to_check }
        is_in_use = true; break
      end
      # 检查快捷启动区
      if (disk['quick_actions'] || []).compact.any? { |cmd| cmd['icon_path'] == icon_path_to_check }
        is_in_use = true; break
      end
    end
    return if is_in_use
  
    # 3. --- 【核心修正：新增对DOCK栏的检查】 ---
    (current_configs['dock_bars'] || []).each do |dock_bar|
      # 检查DOCK栏中的命令和文件夹
      if (dock_bar['commands'] || []).any? { |item| item && item['icon_path'] == icon_path_to_check }
        is_in_use = true; break
      end
    end
    return if is_in_use
    # --- 【修正结束】 ---
  
    # 4. 如果遍历完所有区域后，图标仍未被使用，则执行删除
    full_path = File.join(DATA_DIR, icon_path_to_check)
    if File.exist?(full_path)
      begin
        File.delete(full_path)
        puts "已清理未被引用的图标文件: #{full_path}"
      rescue => e
        puts "错误: 清理图标文件失败: #{e.message}"
      end
    end
  end

  # 【更新】增加对圆盘中心图标的编码功能
  def self.export_settings
    path = UI.savepanel("导出配置", "", "大锋插件管理器配置.oneconfig")
    return unless path
    path += ".oneconfig" unless path.downcase.end_with?(".oneconfig")
  
    begin
      configs_to_export = JSON.parse(get_configs.to_json)
  
      # --- 构建一个包含所有需要处理图标的项目的列表 ---
      all_items_with_icons =
        (configs_to_export['custom_toolbars'] || []).flat_map { |tb| tb['commands'] } +
        (configs_to_export['radial_disks'] || []).flat_map do |d|
          # 【新增】将圆盘本身也视为一个带图标的项目
          items = (d['rings'] || []).flatten.compact + (d['quick_actions'] || []).compact
          items << d if d['center_icon_path'] # 如果有中心图标，则加入处理列表
          items
        end +
        (configs_to_export['dock_bars'] || []).flat_map { |db| db['commands'] }
      # --- 修正结束 ---
  
      all_items_with_icons.each do |item|
        icon_path_key = item.key?('center_icon_path') ? 'center_icon_path' : 'icon_path'
        next unless item && item[icon_path_key]
        
        icon_full_path = File.join(DATA_DIR, item[icon_path_key])
        if File.exist?(icon_full_path)
          image_data = File.binread(icon_full_path)
          base64_string = Base64.strict_encode64(image_data)
          item['icon_base64_data'] = base64_string
        end
      end
      
      File.open(path, "w:UTF-8") do |file|
        file.write(JSON.pretty_generate(configs_to_export))
      end
      
      UI.messagebox("配置已成功导出到:\n#{path}", MB_OK, "导出成功")
    rescue => e
      UI.messagebox("导出失败: #{e.message}", MB_OK, "错误")
    end
  end

  # 【更新】增加对圆盘中心图标的解码和保存功能
  def self.import_settings
    confirm = UI.messagebox("警告：导入配置将会完全覆盖您当前的设置。\n此操作无法撤销，是否继续？", MB_YESNO, "确认导入")
    return unless confirm == IDYES
    filter = Sketchup.platform == :platform_win ? "大锋插件管理器配置 (*.oneconfig)|*.oneconfig||" : "*.oneconfig"
    path = UI.openpanel("选择要导入的配置文件", "", filter)
    return unless path

    begin
      json_string = File.read(path, encoding: 'UTF-8')
      imported_configs = JSON.parse(json_string)
      
      unless imported_configs.is_a?(Hash) && imported_configs.key?('custom_toolbars') && imported_configs.key?('radial_disks')
        raise "这不是一个有效的 大锋插件管理器 配置文件。"
      end

      icons_dir = File.join(DATA_DIR, 'icons')
      FileUtils.mkdir_p(icons_dir)

      # --- 构建一个包含所有需要处理图标的项目的完整列表 ---
      all_imported_items_with_icons =
        (imported_configs['custom_toolbars'] || []).flat_map { |tb| tb['commands'] } +
        (imported_configs['radial_disks'] || []).flat_map do |d|
          items = (d['rings'] || []).flatten.compact + (d['quick_actions'] || []).compact
          items << d if d['center_icon_path']
          items
        end +
        (imported_configs['dock_bars'] || []).flat_map { |db| db['commands'] }
      # --- 修正结束 ---
      
      all_imported_items_with_icons.each do |item|
        if item && item['icon_base64_data']
          icon_path_key = item.key?('center_icon_path') ? 'center_icon_path' : 'icon_path'
          next unless item[icon_path_key]
          destination_path = File.join(DATA_DIR, item[icon_path_key])
          File.open(destination_path, 'wb') { |f| f.write(Base64.decode64(item['icon_base64_data'])) }
        end
        item.delete('icon_base64_data')
      end

      save_configs(imported_configs)
      
      load_and_recreate_custom_toolbars
      load_and_recreate_radial_commands
      UI.start_timer(0.1, false) { show_or_update_docks(Sketchup.active_model) }
      refresh_ui(true)
      
      UI.messagebox("配置已成功导入！", MB_OK, "导入成功")
    rescue JSON::ParserError
      UI.messagebox("导入失败：文件不是一个有效的JSON格式。", MB_OK, "文件格式错误")
    rescue => e
      UI.messagebox("导入失败: #{e.message}", MB_OK, "错误")
    end
  end

    # 【新增】为快捷动作条的命令设置快捷键
  def self.set_quick_action_key(disk_id, slot_index, key)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk && disk['quick_actions'] && disk['quick_actions'][slot_index]

    # 【核心修改】清理并验证快捷键（只接受单个数字或-、=）
    valid_key = key.to_s.strip[0] # 只取第一个字符
    unless valid_key && valid_key.match?(/^[\d\-=]$/)
      valid_key = nil # 如果输入无效（不是数字、-、=），则清空快捷键
    end

    # 检查快捷键是否在该圆盘的快捷动作条中已存在 (此部分逻辑不变)
    is_duplicate = (disk['quick_actions'] || []).each_with_index.any? do |action, index|
      action && action['key'] == valid_key && index != slot_index
    end

    if valid_key && is_duplicate
      UI.messagebox("设置失败：快捷键 '#{valid_key}' 在此圆盘的快捷动作条中已被使用。", MB_OK, "快捷键冲突")
      # 【重要】刷新UI以将输入框恢复原状
      refresh_ui(false)
      return
    end

    disk['quick_actions'][slot_index]['key'] = valid_key
    save_configs(configs)
    # 此处无需刷新UI，避免输入框失去焦点
  end

  def self.add_or_reorder_command(toolbar_name, command_id, target_command_id, is_after, base64_data = nil)
    configs = get_configs
    configs['custom_toolbars'] ||= []
    original_cmd = @live_command_cache[command_id]
    return unless original_cmd
    
    # 图标处理逻辑保持不变
    icons_dir = File.join(DATA_DIR, 'icons')
    FileUtils.mkdir_p(icons_dir)
    unique_filename = "#{Digest::MD5.hexdigest(command_id)}.png"
    dest_abs = File.join(icons_dir, unique_filename)
    dest_rel = File.join('icons', unique_filename)
    
    if !File.exist?(dest_abs)
      image_binary_data = nil
      if base64_data && !base64_data.empty?
        image_binary_data = Base64.decode64(base64_data)
      else
        src_path = original_cmd.large_icon.empty? ? original_cmd.small_icon : original_cmd.large_icon
        if src_path && File.exist?(src_path)
          image_binary_data = File.binread(src_path)
        end
      end

      # 只要成功获取了图像数据，就调用我们的透明化方法进行处理并保存
      if image_binary_data
        make_icon_background_transparent_and_save(image_binary_data, dest_abs)
      end
    end
    
    command_data = {
      'type' => 'command',
      'unique_id' => command_id, 
      'tooltip' => original_cmd.tooltip.force_encoding("UTF-8"), 
      'icon_path' => dest_rel
    }
    
    # 从所有自定义工具栏中移除该命令（处理从一个工具栏移动到另一个的情况）
    configs['custom_toolbars'].each { |tb| tb['commands'].reject! { |cmd| cmd && cmd['unique_id'] == command_id } }
    
    # 找到目标工具栏
    target_toolbar = configs['custom_toolbars'].find { |c| c['name'] == toolbar_name }
    return unless target_toolbar
    
    commands_list = target_toolbar['commands']
    
    # --- 【核心修正】在查找时，同时检查 'id' 和 'unique_id' ---
    index = target_command_id ? commands_list.find_index { |item| item && (item['unique_id'] == target_command_id || item['id'] == target_command_id) } : nil
    
    if index
      commands_list.insert(is_after ? index + 1 : index, command_data)
    else
      commands_list << command_data
    end
    
    save_configs(configs)
    #refresh_ui(false) # 【重要】操作完成后刷新UI
  end

  def self.add_separator_to_toolbar(toolbar_name, target_command_id, is_after)
    configs = get_configs
    
    target_toolbar = (configs['custom_toolbars'] || []).find { |tb| tb['name'] == toolbar_name }
    return unless target_toolbar
  
    separator_data = {
      'type' => 'separator',
      'id' => SecureRandom.uuid
    }
  
    commands_list = target_toolbar['commands']
    index = target_command_id ? commands_list.find_index { |item| item && (item['unique_id'] == target_command_id || item['id'] == target_command_id) } : nil
    
    if index
      commands_list.insert(is_after ? index + 1 : index, separator_data)
    else
      commands_list << separator_data
    end
  
    save_configs(configs)
  end

  # 【新增】在自定义工具栏的末尾添加一个分隔符
  def self.add_separator_to_toolbar_end(toolbar_name)
    configs = get_configs
    
    target_toolbar = (configs['custom_toolbars'] || []).find { |tb| tb['name'] == toolbar_name }
    return unless target_toolbar
  
    separator_data = {
      'type' => 'separator',
      'id' => SecureRandom.uuid
    }
  
    target_toolbar['commands'] << separator_data
    save_configs(configs)
  end

  # 【新增】处理将命令添加到DOCK栏的逻辑
  def self.add_command_to_dock_bar(dock_id, command_id, target_command_id, is_after, base64_data)
    configs = get_configs
    target_dock_bar = (configs['dock_bars'] || []).find { |db| db['id'] == dock_id }
    return unless target_dock_bar
  
    if (target_dock_bar['commands'] || []).any? { |cmd| cmd && cmd['unique_id'] == command_id }
      UI.messagebox("此命令已存在于当前DOCK栏中。", MB_OK)
      return
    end
    
    original_cmd = @live_command_cache[command_id]
    return unless original_cmd
  
    # --- 【核心修正】图标处理逻辑 ---
    # 现在我们接收并处理base64_data，而不是复制原始文件
    icons_dir = File.join(DATA_DIR, 'icons')
    FileUtils.mkdir_p(icons_dir)
    unique_filename = "#{Digest::MD5.hexdigest(command_id)}.png"
    dest_abs = File.join(icons_dir, unique_filename)
    dest_rel = File.join('icons', unique_filename)
    
    if !File.exist?(dest_abs)
      image_binary_data = nil
      if base64_data && !base64_data.empty?
        # 1. 从base64数据解码，获取图像的二进制内容
        image_binary_data = Base64.decode64(base64_data)
      else
        # 2. 备用逻辑：从原始命令的图标文件路径读取二进制内容
        puts "警告: 未能从前端获取图标数据，尝试直接读取文件..."
        src_path = original_cmd.large_icon.empty? ? original_cmd.small_icon : original_cmd.large_icon
        if src_path && File.exist?(src_path)
          image_binary_data = File.binread(src_path)
        end
      end

      # 3. 只要成功获取了图像数据，就调用我们的新辅助方法进行透明化处理并保存
      if image_binary_data
        make_icon_background_transparent_and_save(image_binary_data, dest_abs)
      end
    end
  
    command_data = {
      'type' => 'command',
      'unique_id' => command_id,
      'tooltip' => original_cmd.tooltip.force_encoding("UTF-8"),
      'icon_path' => dest_rel
    }
  
    # 插入逻辑保持不变
    commands_list = target_dock_bar['commands']
    index = target_command_id ? commands_list.find_index { |item| item && (item['unique_id'] == target_command_id || item['id'] == target_command_id) } : nil
    
    if index
      commands_list.insert(is_after ? index + 1 : index, command_data)
    else
      commands_list << command_data
    end
  
    save_configs(configs)
  end

  def self.add_separator_to_dock_bar(dock_id)
    configs = get_configs
    
    target_dock_bar = (configs['dock_bars'] || []).find { |db| db['id'] == dock_id }
    return unless target_dock_bar
  
    # 创建一个代表分隔符的特殊对象
    separator_data = {
      'type' => 'separator',
      'id' => SecureRandom.uuid # 同样给一个唯一ID，便于将来可能的拖拽排序
    }
  
    target_dock_bar['commands'] << separator_data
    save_configs(configs)
  end

  # 【新增】创建文件夹并添加到DOCK栏
  def self.add_folder_to_dock_bar(dock_id, source_toolbar_name, cover_cmd_id, icon_data, is_emoji)
    configs = get_configs
    dock_bar = (configs['dock_bars'] || []).find { |db| db['id'] == dock_id }
    return unless dock_bar
  
    cover_icon_path = nil
    
    # 因为所有图标都已转为Base64，所以我们统一处理
    if icon_data && !icon_data.empty?
      icons_dir = File.join(DATA_DIR, 'icons')
      FileUtils.mkdir_p(icons_dir)
      # 使用 cover_cmd_id 来生成文件名，确保复用性
      unique_filename = "#{Digest::MD5.hexdigest(cover_cmd_id)}.png"
      dest_abs = File.join(icons_dir, unique_filename)
      dest_rel = File.join('icons', unique_filename)
      
      # 仅在文件不存在时写入，避免重复IO操作
      unless File.exist?(dest_abs)
        image_binary_data = Base64.decode64(icon_data)
        make_icon_background_transparent_and_save(image_binary_data, dest_abs)
      end
      cover_icon_path = dest_rel
    end
  
    # 创建文件夹数据，不再需要 icon_emoji 字段
    folder_data = {
      'type' => 'folder',
      'id' => SecureRandom.uuid,
      'source_toolbar_name' => source_toolbar_name,
      'icon_path' => cover_icon_path, # 统一使用 icon_path
      'tooltip' => "文件夹: #{source_toolbar_name}"
    }
  
    dock_bar['commands'] ||= []
    dock_bar['commands'] << folder_data
    save_configs(configs)
    precache_folder_icons_async(source_toolbar_name)
  end

  def self.reorder_dock_item(dock_id, source_item_id, target_item_id, is_after)
    configs = get_configs
    dock_bar = (configs['dock_bars'] || []).find { |db| db['id'] == dock_id }
    return unless dock_bar
  
    commands_list = dock_bar['commands']
    
    # --- 【核心修正】在查找时，同时检查 'id' 和 'unique_id' ---
    source_item_index = commands_list.find_index { |item| item && (item['id'] == source_item_id || item['unique_id'] == source_item_id) }
    source_item = commands_list.delete_at(source_item_index) if source_item_index
    return unless source_item
  
    if target_item_id
      target_index = commands_list.find_index { |item| item && (item['id'] == target_item_id || item['unique_id'] == target_item_id) }
      if target_index
        # 如果目标是自己，则不做任何事（虽然前端逻辑已处理，但后端也做防御）
        return if source_item['id'] == target_item_id || source_item['unique_id'] == target_item_id
        commands_list.insert(is_after ? target_index + 1 : target_index, source_item)
      else
        commands_list << source_item
      end
    else
      commands_list << source_item
    end
  
    save_configs(configs)
  end

  def self.reorder_toolbar_item(toolbar_name, source_item_id, target_item_id, is_after)
    configs = get_configs
    toolbar = (configs['custom_toolbars'] || []).find { |tb| tb['name'] == toolbar_name }
    return unless toolbar
  
    commands_list = toolbar['commands']
    
    source_item_index = commands_list.find_index { |item| item && (item['id'] == source_item_id || item['unique_id'] == source_item_id) }
    source_item = commands_list.delete_at(source_item_index) if source_item_index
    return unless source_item
  
    if target_item_id
      target_index = commands_list.find_index { |item| item && (item['id'] == target_item_id || item['unique_id'] == target_item_id) }
      if target_index
        commands_list.insert(is_after ? target_index + 1 : target_index, source_item)
      else
        commands_list << source_item
      end
    else
      commands_list << source_item
    end
  
    save_configs(configs)
  end

  # 【新增】将一个DOCK栏分配到一个停靠槽中
  def self.assign_dock_bar_to_slot(dock_id, slot_name, target_dock_id, is_after)
    configs = get_configs
    configs['dock_bar_assignments'] ||= { 'top' => [], 'bottom' => [], 'right' => [] }
  
    # 1. 首先从所有停靠槽中移除这个DOCK栏，确保它只存在于一个地方
    configs['dock_bar_assignments'].each_value { |ids| ids.delete(dock_id) }
  
    # 2. 将其添加到新的目标停靠槽中
    target_slot_list = configs['dock_bar_assignments'][slot_name]
    
    if target_dock_id
      target_index = target_slot_list.index(target_dock_id)
      if target_index
        target_slot_list.insert(is_after ? target_index + 1 : target_index, dock_id)
      else
        target_slot_list << dock_id # 如果目标ID无效，则添加到末尾
      end
    else
      target_slot_list << dock_id # 如果没有目标，直接添加到末尾
    end
    
    save_configs(configs)
  end
  
  def self.unassign_dock_bar(dock_id)
    configs = get_configs
    return unless configs['dock_bar_assignments']
  
    # 遍历所有停靠槽，找到并删除该ID
    configs['dock_bar_assignments'].each_value do |id_array|
      id_array.delete(dock_id)
    end
  
    save_configs(configs)
  end

  # 【新增】对停靠槽内部的DOCK栏进行排序
  def self.reorder_dock_bar_in_slot(slot_name, ordered_ids)
    configs = get_configs
    configs['dock_bar_assignments'] ||= { 'top' => [], 'bottom' => [], 'right' => [] }
    
    # 直接用前端排好序的ID数组覆盖旧的数组
    configs['dock_bar_assignments'][slot_name] = ordered_ids
    save_configs(configs)
  end

  def self.add_separator_after_item(dock_id, item_id)
    configs = get_configs
    dock_bar = (configs['dock_bars'] || []).find { |db| db['id'] == dock_id }
    return unless dock_bar
  
    commands_list = dock_bar['commands']
    
    # --- 【核心修正】在查找时，同时检查 'id' 和 'unique_id' ---
    target_index = commands_list.find_index { |item| item && (item['id'] == item_id || item['unique_id'] == item_id) }
  
    if target_index
      separator_data = { 'type' => 'separator', 'id' => SecureRandom.uuid }
      commands_list.insert(target_index + 1, separator_data)
      save_configs(configs)
    else
      # 增加一个日志，以便在找不到目标时我们能知道
      puts "警告：在 add_separator_after_item 中未能找到目标ID: #{item_id}"
    end
  end

  def self.delete_command_from_dock_bar(dock_id, command_id)
    configs = get_configs
    dock_bar = (configs['dock_bars'] || []).find { |db| db['id'] == dock_id }
    return unless dock_bar
  
    command_to_delete = dock_bar['commands'].find { |cmd| cmd && cmd['unique_id'] == command_id }
    dock_bar['commands'].reject! { |cmd| cmd && cmd['unique_id'] == command_id }
    
    # 检查并清理图标文件（复用已有逻辑）
    check_and_delete_icon(command_to_delete['icon_path'], configs) if command_to_delete && command_to_delete['icon_path']
    
    save_configs(configs)
  end

  # 从DOCK栏中删除一个非命令项目 (最终正确版)
  def self.delete_item_from_dock_bar(dock_id, item_id)
    configs = get_configs
    dock_bar = (configs['dock_bars'] || []).find { |db| db['id'] == dock_id }
    return unless dock_bar
  
    item_to_delete = nil
    # 使用 .find 和 .dup 来安全地获取要删除的对象
    dock_bar['commands'].each { |cmd| item_to_delete = cmd.dup if cmd['id'] == item_id }
    return unless item_to_delete
  
    # 【步骤一】从内存配置中移除项目
    dock_bar['commands'].reject! { |item| item && item['id'] == item_id }
  
    # 【步骤二】基于【修改后】的内存配置，执行清理检查
    if item_to_delete['type'] == 'folder'
      # 清理封面图标
      # 注意：我们将修改后的 configs 变量传递给检查函数
      check_and_delete_icon(item_to_delete['icon_path'], configs) if item_to_delete['icon_path']
      
      # 清理内部图标缓存目录
      # 同样，传递修改后的 configs 变量
      cleanup_folder_cache(item_to_delete['source_toolbar_name'], configs)
    end
    
    # 【步骤三】最后，将最终的配置保存到硬盘
    save_configs(configs)
  end

  def self.prompt_and_rename_toolbar(old_name)
    result = UI.inputbox(["新名称:"], [old_name], "重命名工具条 '#{old_name}'")
    
    if result && result[0] && !(new_name = result[0].strip).empty? && new_name.casecmp(old_name) != 0
      configs = get_configs
      
      if (configs['custom_toolbars'] || []).any? { |c| c['name'].casecmp(new_name) == 0 }
        UI.messagebox("重命名失败：已存在名为 \"#{new_name}\" 的工具条。", MB_OK, "名称冲突")
        return
      end

      tb_config = (configs['custom_toolbars'] || []).find { |c| c['name'] == old_name }
      if tb_config
        tb_config['name'] = new_name
        save_configs(configs)
        refresh_ui(false)
        # 【核心修正】在消息和标题之间，添加 MB_OK 作为按钮类型参数
        # UI.messagebox("工具条已重命名为 '#{new_name}'。\n下次启动 SketchUp 时生效。", MB_OK, "重命名成功")
      end
    end
  end

  def self.reorder_toolbars(ordered_names)
    configs = get_configs
    # 直接用前端发来的、已排好序的名称数组覆盖旧的顺序
    configs['custom_toolbars_order'] = ordered_names
    save_configs(configs)
    
    # 只需要刷新UI，让列表重新渲染即可
    refresh_ui(false)
  end

    # 【新增】处理圆盘内部环形区域命令的移动或交换
  def self.move_or_swap_radial_command(disk_id, source_ring_index, source_slot_index, target_ring_index, target_slot_index)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk && disk['rings']

    source_ring = disk['rings'][source_ring_index]
    target_ring = disk['rings'][target_ring_index]

    return unless source_ring && target_ring # 确保源和目标环都存在

    source_command = source_ring[source_slot_index]
    target_command = target_ring[target_slot_index]

    # 执行交换操作
    target_ring[target_slot_index] = source_command # 将源命令放到目标位置
    source_ring[source_slot_index] = target_command # 将目标命令放到源位置

    save_configs(configs)
    refresh_ui(false) # 刷新UI以反映变化
    @dialog.execute_script("showStatusFeedback('圆盘命令已移动。')")
  end

  # 【最终重构版】确保项目在移动时，会从其旧的父容器中被正确移除
  def self.update_radial_item_position(data)
    configs = get_configs
    dragged_id = data['draggedId']
    target_id = data['targetId']
    position = data['position']
    
    # 1. 【核心】首先，从所有可能的位置（主列表和所有分组）中彻底移除被拖动的项
    (configs['radial_disks_order'] || []).delete(dragged_id)
    (configs['radial_groups'] || []).each { |g| (g['disk_ids'] || []).delete(dragged_id) }
    
    # 2. 然后，根据拖放的位置，将其“干净地”插入到唯一的新位置
    if position == 'inside'
      # 情况A：拖入一个分组
      target_group = (configs['radial_groups'] || []).find { |g| g['id'] == target_id }
      if target_group
        target_group['disk_ids'] ||= []
        # 直接添加到末尾，因为组内排序由另一个方法处理
        target_group['disk_ids'] << dragged_id
      end
    else
      # 情况B：在顶层列表（或分组内部）进行排序
      # 找出目标项目所在的列表是哪个（是顶层列表，还是某个分组的列表）
      parent_list_for_target = nil
      if (configs['radial_disks_order'] || []).include?(target_id)
        parent_list_for_target = configs['radial_disks_order']
      else
        parent_group = (configs['radial_groups'] || []).find { |g| (g['disk_ids'] || []).include?(target_id) }
        parent_list_for_target = parent_group['disk_ids'] if parent_group
      end

      # 如果找不到目标项目所在的列表（例如拖到了空白处），则默认添加到顶层列表
      parent_list_for_target ||= configs['radial_disks_order']

      # 在找到的列表中进行插入操作
      if target_id && (target_index = parent_list_for_target.index(target_id))
        insert_index = position == 'before' ? target_index : target_index + 1
        parent_list_for_target.insert(insert_index, dragged_id)
      else
        # 如果没有明确的目标，则添加到列表末尾
        parent_list_for_target << dragged_id
      end
    end

    save_configs(configs)
    refresh_ui(true) # 使用 true 来强制重新加载，确保菜单快捷键等也能更新
  end

  # 【新增】处理分组内部圆盘重新排序请求的方法
  def self.reorder_disks_in_group(group_id, ordered_disk_ids)
    configs = get_configs
    target_group = (configs['radial_groups'] || []).find { |g| g['id'] == group_id }
    
    if target_group
      # 直接用前端发来的、已排好序的ID数组覆盖旧的顺序
      target_group['disk_ids'] = ordered_disk_ids
      save_configs(configs)
      refresh_ui(false)
    end
  end

  # 【替换】整个方法，增加对被截断命令的图标清理逻辑
  def self.update_disk_layout(disk_id, ring_slot_counts, qa_slot_count)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk

    commands_to_check_for_cleanup = []

    # --- 1. 更新圆环布局 ---
    disk['slot_counts'] = ring_slot_counts
    disk['rings'] ||= [ [], [], [] ]

    3.times do |i|
      current_ring = disk['rings'][i] || []
      new_size = ring_slot_counts[i]
      
      if new_size < current_ring.length
        # 【核心修复】在截断前，记录下被移除的命令
        commands_to_check_for_cleanup.concat(current_ring[new_size..-1].compact)
        current_ring.slice!(new_size..-1)
      elsif new_size > current_ring.length
        current_ring[new_size - 1] = nil # 扩展数组
      end
      disk['rings'][i] = current_ring
    end

    # --- 2. 更新快捷动作条布局 ---
    disk['quick_action_slot_count'] = qa_slot_count
    quick_actions = disk['quick_actions'] || []

    if qa_slot_count < quick_actions.length
      # 【核心修复】同样记录快捷动作条中被移除的命令
      commands_to_check_for_cleanup.concat(quick_actions[qa_slot_count..-1].compact)
      quick_actions.slice!(qa_slot_count..-1)
    elsif qa_slot_count > quick_actions.length
      quick_actions[qa_slot_count - 1] = nil
    end
    disk['quick_actions'] = quick_actions

    # 3. 保存修改后的配置
    save_configs(configs)
    
    # 4. 【核心修复】对所有被移除的命令，执行图标清理检查
    commands_to_check_for_cleanup.each do |cmd|
      check_and_delete_icon(cmd['icon_path'], get_configs) if cmd && cmd['icon_path']
    end
    
    # 5. 刷新UI
    refresh_ui(false)
  end

  # 【新增】在指定位置插入一个新的空槽位
  def self.insert_radial_slot(disk_id, ring_index, slot_index)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk && disk['rings'] && disk['rings'][ring_index]

    # 【核心修复】在插入前，检查是否已达到该环的最大槽位数
    current_count = disk['rings'][ring_index].length
    limit = RING_LIMITS[ring_index]
    if current_count >= limit[:max]
      UI.messagebox("操作失败：该环最多只能有 #{limit[:max]} 个槽位。", MB_OK, "已达上限")
      return
    end

    disk['rings'][ring_index].insert(slot_index, nil)
    disk['slot_counts'][ring_index] += 1
    
    save_configs(configs)
    refresh_ui(false)
  end

  # 【新增】删除一个指定的槽位（及其中的图标）
  def self.delete_radial_slot(disk_id, ring_index, slot_index)
    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk && disk['rings'] && disk['rings'][ring_index]

    # 【核心修复】在删除前，检查是否已达到该环的最小槽位数
    current_count = disk['rings'][ring_index].length
    limit = RING_LIMITS[ring_index]
    if current_count <= limit[:min]
      UI.messagebox("操作失败：该环最少需要有 #{limit[:min]} 个槽位。", MB_OK, "已达下限")
      return
    end

    command_to_check = disk['rings'][ring_index][slot_index]
    disk['rings'][ring_index].delete_at(slot_index)
    disk['slot_counts'][ring_index] -= 1

    save_configs(configs)
    
    if command_to_check && command_to_check['icon_path']
      check_and_delete_icon(command_to_check['icon_path'], get_configs)
    end
    
    refresh_ui(false)
  end

  def self.load_and_recreate_custom_toolbars
    configs = get_configs
    custom_toolbars = configs['custom_toolbars'] || []
    current_names = custom_toolbars.map { |c| c['name'] }.to_set
    
    # 隐藏并移除那些在新的配置中已被删除的工具栏
    @created_toolbars.keys.each do |name|
      @created_toolbars.delete(name)&.hide if !current_names.include?(name)
    end

    custom_toolbars.each do |config|
      name = config['name']
      commands_data = config['commands']
      next if name.nil? || name.empty?
      
      # --- 【核心修复开始】 ---
      # 1. 如果内存中已存在同名工具栏，先将其隐藏
      if (old_toolbar = @created_toolbars[name])
        old_toolbar.hide
      end
      
      # 2. 创建一个全新的、干净的同名工具栏实例来替代旧的
      toolbar = UI::Toolbar.new(name)
      # --- 【核心修复结束】 ---

      # 后续的添加逻辑保持不变，因为我们现在操作的是一个全新的空工具栏
      (commands_data || []).each do |item_data|
        type = item_data['type'] || 'command' # 兼容旧数据
  
        if type == 'separator'
          toolbar.add_separator
        elsif type == 'command'
          unique_id = item_data['unique_id']
          tooltip = item_data['tooltip'] || "未知命令"
        
          proxy_cmd = UI::Command.new(tooltip) {
            original_cmd = @live_command_cache[unique_id]
            if original_cmd.nil?
              OneToolbarCreator.cache_all_command_data
              original_cmd = @live_command_cache[unique_id]
            end
            if original_cmd&.proc.is_a?(Proc)
              UI.start_timer(0, false) { original_cmd.proc.call }
            else
              UI.messagebox("命令 '#{tooltip}' 未加载或无法执行。", MB_OK, "命令执行失败")
            end
          }

          if (icon_path = item_data['icon_path']) && !icon_path.empty?
            abs_path = File.join(DATA_DIR, icon_path)
            proxy_cmd.large_icon = proxy_cmd.small_icon = abs_path if File.exist?(abs_path)
          end
          toolbar.add_item(proxy_cmd)
        end
      end

      # 将新创建并填充好的工具栏显示出来，并更新到我们的内存缓存中
      toolbar.restore if toolbar.length > 0
      @created_toolbars[name] = toolbar
    end
    
    begin
      $one_custom_toolbars ||= Set.new
      $one_custom_toolbars.clear
      custom_toolbars.each { |c| $one_custom_toolbars.add(c['name']) }
    rescue => e
      puts "[OneToolbarCreator] 注册全局工具栏名称时出错: #{e.message}"
    end
  end
  
  unless file_loaded?(__FILE__)
    Sketchup.add_observer(AppShutdownObserver.new)
    
    # 【修改】创建一次性的主菜单和子菜单
    @main_menu = UI.menu("Plugins").add_submenu(PLUGIN_NAME)
    @main_menu.add_item("设置面板") { show_dialog }
    
    # 预先创建好圆盘菜单的容器，但此时是空的
    @radial_commands_submenu = @main_menu.add_submenu("圆盘菜单")

        # --- 【新增】为设置面板创建专属工具栏 ---

    # 1. 创建一个命令对象，其执行内容就是调用 show_dialog 方法
    cmd_show_dialog = UI::Command.new("打开设置面板") do
      show_dialog
    end
    cmd_show_dialog.tooltip = "打开 大锋插件管理器 设置面板"
    cmd_show_dialog.status_bar_text = "创建和管理您的自定义工具栏与圆盘菜单"

    # 2. 根据您提供的路径，构造图标文件的完整路径
    #    __FILE__ 会获取当前文件的路径，File.dirname 则得到该文件所在的目录
    icon_path = File.join(File.dirname(__FILE__), 'ICON', 'ICON_Toolbar.png')

    # 3. 为命令设置图标（先检查文件是否存在，避免报错）
    if File.exist?(icon_path)
      cmd_show_dialog.small_icon = icon_path
      cmd_show_dialog.large_icon = icon_path
    else
      # 如果找不到图标，在Ruby控制台打印一条警告信息
      puts "警告：[大锋插件管理器] 未能找到工具栏图标文件：#{icon_path}"
    end

    # 4. 创建一个新的工具栏，并把上面的命令添加进去
    one_main_toolbar = UI::Toolbar.new(PLUGIN_NAME) # 使用插件名称作为工具栏的标题
    one_main_toolbar.add_item(cmd_show_dialog)

    # 5. 让工具栏默认显示出来
    one_main_toolbar.restore
    

    
    # 插件启动时，加载所有工具栏和圆盘菜单
    UI.start_timer(0, false) do
      load_and_recreate_custom_toolbars
      load_and_recreate_radial_commands 
    end

        # --- 【新增代码：注册观察者并初始化当前模型】 ---
    app_observer = OneToolbarAppObserver.new
    Sketchup.add_observer(app_observer)

    reset_dock_indices # 【新增】插件加载时，重置显示索引
    
    # 为插件启动时就已经激活的模型，手动执行一次DOCK栏加载
    UI.start_timer(1, false) do # 延迟1秒执行，确保SU完全准备好
        show_or_update_docks(Sketchup.active_model)
    end
    # --- 【新增结束】 ---

    file_loaded(__FILE__)
  end

  def self.prompt_and_create_dock_bar
    result = UI.inputbox(["请输入新DOCK栏的名称:"], ["新的DOCK栏"], "创建新DOCK栏")
    if result && result[0] && !(name = result[0].strip).empty?
      
      configs = get_configs
      # 复用我们已有的唯一名称生成器
      existing_names = (configs['dock_bars'] || []).map { |db| db['name'] }
      unique_name = generate_unique_name(name, existing_names)
      
      create_dock_bar(unique_name) 
      refresh_ui(false) 
    end
  end

  def self.prompt_and_rename_dock_bar(dock_id, old_name)
    result = UI.inputbox(["请输入新的DOCK栏名称:"], [old_name], "重命名DOCK栏")
    if result && result[0] && !(new_name = result[0].strip).empty? && new_name != old_name
      configs = get_configs
      
      # 检查重名
      if (configs['dock_bars'] || []).any? { |db| db['id'] != dock_id && db['name'].casecmp(new_name) == 0 }
        UI.messagebox("重命名失败：已存在名为 \"#{new_name}\" 的DOCK栏。", MB_OK)
        return
      end
  
      dock_bar = (configs['dock_bars'] || []).find { |db| db['id'] == dock_id }
      if dock_bar
        dock_bar['name'] = new_name
        save_configs(configs)
        refresh_ui(false)
      end
    end
  end
  
  def self.confirm_and_delete_dock_bar(dock_id, name)
    if UI.messagebox("确定要永久删除DOCK栏 \"#{name}\" 吗？\n\n(此操作无法撤销)", MB_YESNO, "确认删除") == IDYES
      delete_dock_bar(dock_id)
    end
  end

  # 【新增】一个辅助方法，用于隐藏并移除所有当前显示的DOCK栏
  def self.hide_all_docks(model = nil)
    return unless Sketchup.version.to_i >= 23
    model ||= Sketchup.active_model
    return unless model && model.valid?
  
    @dock_overlays.each_value do |overlay|
      # 只移除属于当前模型的overlay
      model.overlays.remove(overlay) if overlay.valid? && model.overlays.include?(overlay)
    end
    @dock_overlays.clear
  end
  
  # 【修改】show_or_update_docks 方法，使其能读取并传递两个状态
  def self.show_or_update_docks(model = nil, expanded_slot: nil)
    return unless Sketchup.version.to_i >= 23
    
    model ||= Sketchup.active_model
    return unless model && model.valid?
  
    hide_all_docks(model)
  
    configs = get_configs
    assignments = configs['dock_bar_assignments'] || {}
    all_docks_data = get_dock_bar_data
  
    assignments.each do |slot_name, assigned_ids|
      next if assigned_ids.nil? || assigned_ids.empty?
      
      current_index = @dock_display_indices[slot_name] || 0; current_index = 0 if current_index >= assigned_ids.length
      dock_id_to_display = assigned_ids[current_index]
      dock_data = all_docks_data.find { |db| db[:id] == dock_id_to_display }
      next unless dock_data
  
      all_docks_in_slot_data = assigned_ids.map { |id| all_docks_data.find { |db| db[:id] == id } }.compact
  
      begin
        should_start_expanded = (slot_name == expanded_slot)

        # --- 【核心修改】 ---
        # a. 读取控制按钮“是否显示”的设置
        show_button_setting = configs.dig('dock_slot_settings', slot_name, 'show_auto_hide_button')
        show_button_setting = false if show_button_setting.nil? # 默认不显示

        # b. 读取控制DOCK“实际行为”的设置
        behavior_enabled_setting = configs.dig('dock_slot_settings', slot_name, 'auto_hide_enabled')
        behavior_enabled_setting = false if behavior_enabled_setting.nil? # 【核心修改】如果设置不存在，则默认为 false

        # c. 将这两个设置作为新参数传递给 DockOverlay
        overlay = DockOverlay.new(dock_data, all_docks_in_slot_data, slot_name, 
                                  start_expanded: should_start_expanded, 
                                  show_control_button: show_button_setting,
                                  auto_hide_behavior: behavior_enabled_setting)
        # --- 【修改结束】 ---
        
        model.overlays.add(overlay)
        overlay.enabled = true
        @dock_overlays[slot_name] = overlay
      rescue => e
        puts "创建DOCK Overlay失败 (slot: #{slot_name}): #{e.message}\n#{e.backtrace.first}"
      end
    end
  end

  # --- 【完整版】文件夹与图标缓存相关的所有核心方法 ---
  
  # 1. 获取文件夹内的所有命令数据
  def self.get_commands_for_folder(source_toolbar_name)
    # 确保缓存至少被初始化过一次
    ensure_command_cache_is_ready

    # 第一次尝试获取
    commands = _get_commands_for_folder_internal(source_toolbar_name)

    # 如果第一次没找到，说明可能是新插件加载了，我们强制刷新缓存再试一次
    if commands.empty?
      puts "[OneToolbar] Toolbar '#{source_toolbar_name}' not found in cache. Recaching all commands..."
      cache_all_command_data
      # 第二次尝试获取
      commands = _get_commands_for_folder_internal(source_toolbar_name)
    end

    return commands
  end

  # 2. 用这个【务实缓存版】替换掉现有的 ensure_icons_are_cached 方法
  def self.ensure_icons_are_cached(source_toolbar_name)
    commands = get_commands_for_folder(source_toolbar_name)
    return true if commands.empty?
  
    cache_dir_name = Digest::MD5.hexdigest(source_toolbar_name)
    cache_dir = File.join(DATA_DIR, 'icons', cache_dir_name)
    FileUtils.mkdir_p(cache_dir) # 确保子目录存在
  
    placeholder_path = File.join(PLUGIN_DIR, 'ICON', 'placeholder_svg.png')
  
    commands.each do |cmd|
      # 获取原始命令对象以找到其图标路径
      original_cmd = @live_command_cache[cmd[:unique_id]]
      next unless original_cmd
  
      target_filename = "#{Digest::MD5.hexdigest(cmd[:unique_id])}.png"
      target_path = File.join(cache_dir, target_filename)
  
      # 如果缓存文件已存在，则跳过
      next if File.exist?(target_path)
  
      # 获取原始图标路径
      source_path = original_cmd.large_icon.empty? ? original_cmd.small_icon : original_cmd.large_icon
  
      if source_path && !source_path.empty? && File.exist?(source_path)
        if source_path.downcase.end_with?('.svg')
          # 如果是SVG，并且我们有占位符，就复制占位符
          FileUtils.copy_file(placeholder_path, target_path) if File.exist?(placeholder_path)
          #puts "[OneToolbar] SVG icon for '#{cmd[:tooltip]}' cached with placeholder."
        else
          # 对于PNG/JPG等光栅图片，读取其二进制数据
          image_binary_data = File.binread(source_path)
          # 调用全局的透明化辅助方法进行处理和保存
          OneToolbarCreator.make_icon_background_transparent_and_save(image_binary_data, target_path)
          #puts "[OneToolbar] Icon for '#{cmd[:tooltip]}' cached with transparency."
        end
      end
    end
    return true # 表示处理完成
  end
    
  # 3. JS回调：保存单个生成的图标
  def self.save_generated_icon(unique_id, source_toolbar_name, base64_data)
    return unless unique_id && source_toolbar_name && base64_data
  
    # 创建对应的缓存子目录
    cache_dir_name = Digest::MD5.hexdigest(source_toolbar_name)
    cache_dir = File.join(DATA_DIR, 'icons', cache_dir_name)
    FileUtils.mkdir_p(cache_dir)
    
    # 计算并保存文件
    target_filename = "#{Digest::MD5.hexdigest(unique_id)}.png"
    target_path = File.join(cache_dir, target_filename)
    
    File.open(target_path, 'wb') { |f| f.write(Base64.decode64(base64_data)) }
  end

  # 4. 用这个【务实缓存版】替换掉现有的 get_cached_icon_path 方法
  def self.get_cached_icon_path(unique_id, source_toolbar_name)
    return nil unless unique_id && source_toolbar_name
  
    cache_dir_name = Digest::MD5.hexdigest(source_toolbar_name)
    target_filename = "#{Digest::MD5.hexdigest(unique_id)}.png"
    
    # 直接计算并返回最终应有的路径
    File.join(DATA_DIR, 'icons', cache_dir_name, target_filename)
  end

  # 5. 设置/取消全局固定的文件夹
  def self.set_pinned_folder(folder_data)
    @pinned_folder_data = folder_data
    
    configs = get_configs
    if folder_data
      # 将核心信息存入JSON
      configs['pinned_folder'] = {
        "id" => folder_data[:id],
        "source_toolbar_name" => folder_data[:source_toolbar_name]
        # 未来可以扩展存储位置等信息
      }
    else
      configs.delete('pinned_folder')
    end
    save_configs(configs)
    
    # 如果有固定的，则创建Overlay，否则移除
    if folder_data
      show_folder_overlay(folder_data)
    else
      hide_all_folder_overlays # 需要一个新方法来移除所有固定的文件夹
    end
  end
  
  # 6. 获取全局固定的文件夹数据
  def self.get_pinned_folder_data
    configs = get_configs
    return configs['pinned_folder']
  end
  
  # 7. 在屏幕上显示一个固定的FolderOverlay
  def self.show_folder_overlay(folder_data)
    hide_all_folder_overlays
    
    # TODO: 将来这里需要有恢复位置的逻辑
    # 暂时我们先创建一个Overlay实例，但不显示，因为固定功能还没完全做好
    # overlay = FolderOverlay.new(folder_data, {}) 
    # Sketchup.active_model.overlays.add(overlay)
    # @active_folder_overlays ||= []
    # @active_folder_overlays << overlay
  
    puts "TODO: 实现 show_folder_overlay, for #{folder_data[:tooltip]}"
  end
  
  # 8. 隐藏所有固定的文件夹Overlay
  def self.hide_all_folder_overlays
    # 我们需要一个变量来追踪当前显示的固定文件夹Overlay
    # 假设我们将其存放在 @active_folder_overlays 数组中
    (@active_folder_overlays || []).each do |overlay|
      Sketchup.active_model.overlays.remove(overlay) if overlay.valid?
    end
    (@active_folder_overlays || []).clear
    puts "[OneToolbar] All pinned folder overlays hidden."
  end
  
  # 9. 删除文件夹时，清理其缓存
  def self.cleanup_folder_cache(source_toolbar_name, configs)
    # 检查是否还有其他文件夹在引用这个源工具条
    is_still_in_use = (configs['dock_bars'] || []).any? do |dock|
      (dock['commands'] || []).any? do |cmd|
        cmd && cmd['type'] == 'folder' && cmd['source_toolbar_name'] == source_toolbar_name
      end
    end
    
    return if is_still_in_use # 如果还在用，则不清理
    
    # 这是最后一个引用，执行清理
    cache_dir_name = Digest::MD5.hexdigest(source_toolbar_name)
    dir_to_delete = File.join(DATA_DIR, 'icons', cache_dir_name)
    
    if File.directory?(dir_to_delete)
      FileUtils.rm_rf(dir_to_delete)
      puts "[OneToolbar] 已清理文件夹缓存目录: #{dir_to_delete}"
    end
  end

    # 一个公共的、用于绘制圆角矩形的辅助方法
    def self.get_rounded_rect_points(bounds, radius, segments = 8)
      points = []
      # 右上角
      c_tr = Geom::Point3d.new(bounds.max.x - radius, bounds.max.y - radius, 0)
      (0..segments).each do |i|
        angle = Math::PI / 2 * i / segments
        points << Geom::Point3d.new(c_tr.x + radius * Math.cos(angle), c_tr.y + radius * Math.sin(angle), 0)
      end
      # 左上角
      c_tl = Geom::Point3d.new(bounds.min.x + radius, bounds.max.y - radius, 0)
      (0..segments).each do |i|
        angle = Math::PI / 2 + (Math::PI / 2 * i / segments)
        points << Geom::Point3d.new(c_tl.x + radius * Math.cos(angle), c_tl.y + radius * Math.sin(angle), 0)
      end
      # 左下角
      c_bl = Geom::Point3d.new(bounds.min.x + radius, bounds.min.y + radius, 0)
      (0..segments).each do |i|
        angle = Math::PI + (Math::PI / 2 * i / segments)
        points << Geom::Point3d.new(c_bl.x + radius * Math.cos(angle), c_bl.y + radius * Math.sin(angle), 0)
      end
      # 右下角
      c_br = Geom::Point3d.new(bounds.max.x - radius, bounds.min.y + radius, 0)
      (0..segments).each do |i|
        angle = 3 * Math::PI / 2 + (Math::PI / 2 * i / segments)
        points << Geom::Point3d.new(c_br.x + radius * Math.cos(angle), c_br.y + radius * Math.sin(angle), 0)
      end
      points
    end

    # 一个公共的、用于计算圆形点集的辅助方法
    def self.get_circle_points(center, radius, num_segments = 36)
      (0..num_segments).map do |i|
        angle = 2 * Math::PI * i / num_segments
        Geom::Point3d.new(center.x + radius * Math.cos(angle), center.y + radius * Math.sin(angle), 0)
      end
    end

    # 一个公共的、用于根据ID安全执行命令的辅助方法
    def self.execute_command_by_id(unique_id)
      return unless unique_id
      
      # 尝试从缓存中获取命令
      original_cmd = get_live_command(unique_id)
      
      # 如果找不到，则可能是有新插件载入，重新缓存一次再尝试
      if original_cmd.nil?
        puts "[OneToolbar] Command not in cache, recaching..."
        cache_all_command_data
        original_cmd = get_live_command(unique_id)
      end
      
      # 最终检查并执行
      if original_cmd && original_cmd.proc.is_a?(Proc)
        # 使用定时器延迟执行，以避免潜在的工具状态冲突
        UI.start_timer(0, false) { original_cmd.proc.call }
      else
        UI.messagebox("命令 '#{unique_id}' 未加载或无法执行。", MB_OK, "命令执行失败")
      end
    end

    def self.ensure_command_cache_is_ready
      # 只有在缓存未就绪时，才执行缓存操作
      cache_all_command_data unless @is_cache_ready
    end

      # 【新增】从自定义工具条中删除一个指定的分隔符
  def self.delete_separator_from_toolbar(toolbar_name, separator_id)
    configs = get_configs
    
    # 找到对应的工具条
    target_toolbar = (configs['custom_toolbars'] || []).find { |tb| tb['name'] == toolbar_name }
    return unless target_toolbar

    # 从命令列表中，根据ID移除该分隔符
    # 使用 reject! 方法可以高效地移除所有匹配项
    original_count = target_toolbar['commands'].length
    target_toolbar['commands'].reject! { |item| item && item['type'] == 'separator' && item['id'] == separator_id }
    
    # 如果真的有元素被移除了，才保存配置
    if original_count > target_toolbar['commands'].length
      save_configs(configs)
      puts "已从工具条 '#{toolbar_name}' 中删除分隔符: #{separator_id}"
    end
  end

  # 【新增】从DOCK栏中删除一个指定的分隔符
  def self.delete_separator_from_dock(dock_id, separator_id)
    configs = get_configs
    
    # 找到对应的DOCK栏
    target_dock = (configs['dock_bars'] || []).find { |db| db['id'] == dock_id }
    return unless target_dock

    # 从命令列表中，根据ID移除该分隔符
    original_count = target_dock['commands'].length
    target_dock['commands'].reject! { |item| item && item['type'] == 'separator' && item['id'] == separator_id }

    if original_count > target_dock['commands'].length
      save_configs(configs)
      puts "已从DOCK栏 '#{target_dock['name']}' 中删除分隔符: #{separator_id}"
    end
  end

    # 这个方法会命令前端开始处理图标
    def self.precache_folder_icons_async(source_toolbar_name)
      # 确保对话框存在且可见，否则无法与前端通信
      dialog = get_dialog
      return unless dialog && dialog.visible?
  
      # 1. 获取源工具栏的所有命令信息
      commands_to_process = get_commands_for_folder(source_toolbar_name)
      return if commands_to_process.empty?
  
      # 2. 将这些命令信息打包成JSON
      commands_json = JSON.generate(commands_to_process)
      
      # 3. 构造并执行一个JavaScript命令，调用我们在前端将要创建的新函数
      #    我们把整个命令列表一次性发给前端，让前端自己去遍历，效率更高
      js_command = "window.oneToolbarAPI.batchGenerateAndSaveIcons(#{commands_json}, '#{source_toolbar_name}');"
      
      dialog.execute_script(js_command)
      
      # 在Ruby控制台给一个提示，方便调试
      puts "[OneToolbar] 已向前端发送为文件夹 '#{source_toolbar_name}' 预缓存 #{commands_to_process.length} 个图标的指令。"
    end
  
    # 【新增】回调方法：接收并保存前端处理好的单个PNG图标
    # 【修改】此方法现在会为从HTML面板生成和重绘的图标应用透明化
    def self.save_generated_folder_icon(unique_id, source_toolbar_name, base64_data)
      return unless unique_id && source_toolbar_name && base64_data && !base64_data.empty?
  
      cached_path = get_cached_icon_path(unique_id, source_toolbar_name)
      return unless cached_path
      
      FileUtils.mkdir_p(File.dirname(cached_path))
  
      begin
        # 调用我们通用的透明化方法，而不是直接写入文件
        image_binary_data = Base64.decode64(base64_data)
        make_icon_background_transparent_and_save(image_binary_data, cached_path)
      rescue => e
        puts "[OneToolbar] 保存生成的文件夹内图标失败: #{cached_path}. Error: #{e.message}"
      end
    end

    def self.confirm_and_redraw_folder_icons(source_toolbar_name, folder_tooltip)
      # 构造提示信息
      message = "确定要为文件夹 \"#{folder_tooltip}\" 重新生成所有内部图标吗？\n此操作会覆盖现有图标缓存。"
      
      # 弹出SketchUp原生对话框
      if UI.messagebox(message, MB_YESNO, "确认重绘图标") == IDYES
        # 如果用户点击“是”，则调用我们之前创建的引擎方法
        precache_folder_icons_async(source_toolbar_name)
      end
    end

  # 【最终修正版】增加了在移动前对目标DOCK栏的重复性检查
  def self.move_item_between_docks(source_dock_id, target_dock_id, item_id, target_item_id, is_after)
    configs = get_configs
    all_docks = configs['dock_bars'] || []

    source_dock = all_docks.find { |db| db['id'] == source_dock_id }
    target_dock = all_docks.find { |db| db['id'] == target_dock_id }
    return unless source_dock && target_dock

    # 1. 首先，在源DOCK栏中找到要移动的项目的数据，但先不删除
    item_to_move = source_dock['commands'].find { |item| item && (item['unique_id'] == item_id || item['id'] == item_id) }
    return unless item_to_move

    # ===================================================================
    # ▼▼▼ 【新增的核心验证逻辑】 ▼▼▼
    # ===================================================================
    # 2. 检查该项目是否已存在于目标DOCK栏中
    is_duplicate = false
    if item_to_move['type'] == 'command'
      # 如果是命令，通过 unique_id 检查
      if target_dock['commands'].any? { |cmd| cmd && cmd['unique_id'] == item_to_move['unique_id'] }
        is_duplicate = true
        UI.messagebox("移动失败：目标DOCK栏中已存在命令 '#{item_to_move['tooltip']}'。", MB_OK)
      end
    elsif item_to_move['type'] == 'folder'
      # 如果是文件夹，通过 source_toolbar_name 检查
      if target_dock['commands'].any? { |fld| fld && fld['type'] == 'folder' && fld['source_toolbar_name'] == item_to_move['source_toolbar_name'] }
        is_duplicate = true
        UI.messagebox("移动失败：目标DOCK栏中已存在来自 '#{item_to_move['source_toolbar_name']}' 的文件夹。", MB_OK)
      end
    end

    # 如果发现重复，则直接中止操作，不做任何改动
    return if is_duplicate
    # ======================== ▲▲▲ 新增逻辑结束 ▲▲▲ ========================


    # 3. 验证通过后，才从源DOCK栏中正式移除该项目
    source_dock['commands'].reject! { |item| item && (item['unique_id'] == item_id || item['id'] == item_id) }
    
    # 4. 将该项目添加到目标DOCK栏的正确位置
    commands_list = target_dock['commands']
    target_index = target_item_id ? commands_list.find_index { |item| item && (item['unique_id'] == target_item_id || item['id'] == target_item_id) } : nil
    
    if target_index
      commands_list.insert(is_after ? target_index + 1 : target_index, item_to_move)
    else
      commands_list << item_to_move
    end

    # 5. 保存并刷新
    save_configs(configs)
    refresh_ui(false)
  end

    # 【新增】处理项目在不同“自定义工具栏”之间移动的核心方法
    def self.move_item_between_custom_toolbars(source_toolbar_name, target_toolbar_name, item_id, target_item_id, is_after)
      configs = get_configs
      all_toolbars = configs['custom_toolbars'] || []
  
      # 通过名称找到源和目标工具栏
      source_toolbar = all_toolbars.find { |tb| tb['name'] == source_toolbar_name }
      target_toolbar = all_toolbars.find { |tb| tb['name'] == target_toolbar_name }
      return unless source_toolbar && target_toolbar
  
      # 1. 从源工具栏中找到并移除该项目
      item_to_move = nil
      source_toolbar['commands'].reject! do |item|
        if item && (item['unique_id'] == item_id || item['id'] == item_id)
          item_to_move = item.dup
          true
        else
          false
        end
      end
      
      return unless item_to_move
  
      # 2. 将该项目添加到目标工具栏的正确位置
      commands_list = target_toolbar['commands']
      target_index = target_item_id ? commands_list.find_index { |item| item && (item['unique_id'] == target_item_id || item['id'] == target_item_id) } : nil
      
      if target_index
        commands_list.insert(is_after ? target_index + 1 : target_index, item_to_move)
      else
        commands_list << item_to_move
      end
  
      # 3. 保存并刷新
      save_configs(configs)
      refresh_ui(false)
    end

    # 【新增】全局的、统一样式的自定义提示框绘制方法
  def self.draw_custom_tooltip_globally(view, text, position)
    return if text.nil? || text.empty? || position.nil?

    # 1. 定义新样式
    font_family = (Sketchup.platform == :platform_win) ? 'Microsoft YaHei' : 'PingFang SC'
    text_options = { 
      font: font_family, 
      size: 10, 
      color: [240, 240, 240] # 浅白色文字
    }
    bg_color = [80, 80, 80, 160] #【样式1】更浅的半透明灰色背景
    padding = 4
    corner_radius = 4 #【样式2】圆角半径

    # 2. 计算尺寸
    bounds = view.text_bounds(Geom::Point3d.new(0,0,0), text, **text_options)
    bg_width = bounds.width + (padding * 2)
    bg_height = bounds.height + (padding * 2)
    
    # 3. 计算位置 (确保不超出屏幕)
    vp_width = view.vpwidth
    vp_height = view.vpheight
    
    pos_x = position.x + 15
    pos_y = position.y + 15
    
    pos_x = vp_width - bg_width if pos_x + bg_width > vp_width
    pos_y = vp_height - bg_height if pos_y + bg_height > vp_height
    
    bg_bounds = Geom::BoundingBox.new.add(
        Geom::Point3d.new(pos_x, pos_y, 0),
        Geom::Point3d.new(pos_x + bg_width, pos_y + bg_height, 0)
    )

    # 4. 绘制圆角背景 (复用我们已有的方法)
    view.drawing_color = bg_color
    bg_points = self.get_rounded_rect_points(bg_bounds, corner_radius, 4)
    view.draw2d(GL_POLYGON, bg_points)

    # 5. 绘制文字
    text_point = Geom::Point3d.new(pos_x + padding, pos_y + padding, 0)
    # 【样式3】绘制时传入新的字体选项
    view.draw_text(text_point, text, text_options)
  end

  # 【修复版】回调：让用户选择图片文件，并将其转换为Data URI发送给前端
  def self.select_image_file
    path = UI.openpanel("选择一个图标文件", "", "Image Files|*.png;*.jpg;*.jpeg;*.bmp||")
    
    if path && File.exist?(path)
      # 读取文件并编码为Base64
      image_data = File.binread(path)
      base64_string = Base64.strict_encode64(image_data)
      # 获取文件类型
      ext = File.extname(path).downcase
      mime_type = case ext
                  when ".png" then "image/png"
                  when ".jpg", ".jpeg" then "image/jpeg"
                  when ".bmp" then "image/bmp"
                  else "application/octet-stream"
                  end
      # 构建完整的Data URI
      data_uri = "data:#{mime_type};base64,#{base64_string}"
      # 将Data URI发送给JS
      @dialog.execute_script("onCenterIconFileSelected(#{data_uri.to_json})")
    else
      # 如果用户取消选择，也通知JS
      @dialog.execute_script("onCenterIconFileSelected(null)")
    end
  end
  
  # 【修复版】回调：接收前端处理好的Base64数据并保存
  def self.set_disk_center_icon(disk_id, base64_data)
    return unless disk_id && base64_data

    configs = get_configs
    disk = (configs['radial_disks'] || []).find { |d| d['id'] == disk_id }
    return unless disk

    begin
      # 1. 确定保存路径 (逻辑不变)
      icons_dir = File.join(DATA_DIR, 'icons')
      FileUtils.mkdir_p(icons_dir)
      unique_filename = "center_icon_#{disk_id}.png"
      destination_path_abs = File.join(icons_dir, unique_filename)
      destination_path_rel = File.join('icons', unique_filename)

      # 2. 直接解码并保存前端传来的、已经处理好的Base64数据
      image_binary_data = Base64.decode64(base64_data)
      make_icon_background_transparent_and_save(image_binary_data, destination_path_abs)

      # 3. 更新并保存JSON配置 (逻辑不变)
      disk['center_icon_path'] = destination_path_rel
      save_configs(configs)
      
      # 4. 刷新UI (逻辑不变)
      refresh_ui(true)
    rescue => e
      UI.messagebox("保存图标失败: #{e.message}")
    end
  end

  # 【修改】这个方法现在只负责保存“按钮是否显示”的状态
  # 我们重命名它以明确其功能
  def self.set_dock_control_button_visibility(slot_name, is_visible)
    configs = get_configs
    configs['dock_slot_settings'] ||= {}
    configs['dock_slot_settings'][slot_name] ||= {}
    configs['dock_slot_settings'][slot_name]['show_auto_hide_button'] = is_visible
    save_configs(configs)
    # 保存后，立即刷新DOCK栏以显示或隐藏按钮
    show_or_update_docks(Sketchup.active_model)
  end

    # 【新增】这个方法由界面上的按钮调用，只切换DOCK的“行为状态”
  def self.toggle_auto_hide_behavior(slot_name)
    configs = get_configs
    configs['dock_slot_settings'] ||= {}
    configs['dock_slot_settings'][slot_name] ||= {}
    
    # 读取当前行为状态，如果未定义，则默认为 true
    current_behavior = configs['dock_slot_settings'][slot_name]['auto_hide_enabled']
    current_behavior = true if current_behavior.nil?
    
    # 切换行为状态并保存
    new_behavior = !current_behavior
    configs['dock_slot_settings'][slot_name]['auto_hide_enabled'] = new_behavior
    save_configs(configs)
    
    # 通知当前活动的覆盖层更新其内部状态，并重绘
    overlay = @dock_overlays[slot_name]
    if overlay && overlay.respond_to?(:update_auto_hide_behavior)
      overlay.update_auto_hide_behavior(new_behavior)
    end
  end

      # 【新增】一个全局的、用于生成圆形纹理顶点和UV坐标的辅助方法
    def self.get_textured_circle_data(center, radius, num_segments = 48)
      points = []
      uvs = []

      # 首先，添加圆心点和它对应的纹理中心坐标(0.5, 0.5)
      points << center
      uvs << [0.5, 0.5] # 纹理坐标系的中心

      # 然后，计算圆周上的所有顶点和它们对应的UV坐标
      (0..num_segments).each do |i|
        angle = 2 * Math::PI * i / num_segments
        
        # 计算顶点的屏幕坐标
        point_x = center.x + radius * Math.cos(angle)
        point_y = center.y + radius * Math.sin(angle)
        points << Geom::Point3d.new(point_x, point_y, 0)

        # 计算对应的纹理(UV)坐标
        # 将屏幕上的圆周点映射到纹理贴图的圆周上
        u = 0.5 + 0.5 * Math.cos(angle)
        v = 0.5 - 0.5 * Math.sin(angle) # Y轴在纹理坐标中是反的
        uvs << [u, v]
      end

      return points, uvs
    end

  # --- 【新增代码】 ---
  private # 声明下面的方法是私有的

  # --- 【第4次修正版 - 最终修复版】 ---
  private

  # --- 【第6次修正版 - 增加透明跳过逻辑】 --
  def self.make_icon_background_transparent_and_save(image_binary_data, destination_path)
    temp_path = File.join(Dir.tmpdir, "one_toolbar_temp_icon_#{SecureRandom.hex(8)}.png")

    begin
      File.open(temp_path, 'wb') { |f| f.write(image_binary_data) }

      image_rep = Sketchup::ImageRep.new(temp_path)
      width = image_rep.width
      height = image_rep.height

      # 使用 color_at_uv 获取左上角像素。
      # 【重要】需要传递第三个参数 true 来确保我们能获取到正确的Alpha值
      background_color = image_rep.color_at_uv(0, 0, true)

      # 【新增逻辑】: 检查左上角像素的Alpha值
      # 如果它不是完全不透明 (alpha < 255)，就意味着该图标本身很可能
      # 已经有透明背景了，我们应该跳过所有处理，直接使用原图。
      if background_color.alpha < 255
        # 直接复制临时文件到最终路径，然后提前结束方法
        FileUtils.copy_file(temp_path, destination_path)
        return # 提前退出
      end
      
      # --- 如果程序能走到这里，说明左上角是不透明的，我们才执行后续的透明化处理 ---

      new_pixel_bytes_32bit = []

      (height - 1).downto(0) do |y|
        width.times do |x|
          # 这里也要传递 true 以确保获取到正确的Alpha值
          current_color = image_rep.color_at_uv((x + 0.5) / width, (y + 0.5) / height, true)
          
          is_background = ((current_color.red - background_color.red).abs < ICON_BG_TOLERANCE) &&
                          ((current_color.green - background_color.green).abs < ICON_BG_TOLERANCE) &&
                          ((current_color.blue - background_color.blue).abs < ICON_BG_TOLERANCE)
          
          # 【优化】如果是背景，则alpha为0；否则，保留该像素原始的alpha值
          alpha = is_background ? 0 : current_color.alpha
          
          new_pixel_bytes_32bit.push(current_color.blue, current_color.green, current_color.red, alpha)
        end
      end

      new_pixel_data_string = new_pixel_bytes_32bit.pack('C*')
      image_rep.set_data(width, height, 32, 0, new_pixel_data_string)
      image_rep.save_file(destination_path)

    rescue => e
      puts "透明化图标背景时出错: #{e.message}"
      puts e.backtrace.join("\n")
      File.open(destination_path, 'wb') { |f| f.write(image_binary_data) }
    ensure
      File.delete(temp_path) if temp_path && File.exist?(temp_path)
    end
  end

  def self._get_commands_for_folder_internal(source_toolbar_name)
    # 优先从自定义工具栏中查找，因为它的数据结构最稳定
    custom_toolbar_data = get_custom_toolbar_data
    toolbar = custom_toolbar_data.find { |tb| tb[:name] == source_toolbar_name }
    # 如果在自定义工具栏中找到了，就直接返回其所有项目
    return toolbar[:commands] if toolbar && toolbar[:commands]
  
    # 如果在自定义工具栏中没找到，再从系统工具栏中查找
    source_toolbar_data = get_source_command_data
    toolbar = source_toolbar_data.find { |tb| tb[:name] == source_toolbar_name }
    # 如果在系统工具栏中找到了，同样返回其所有项目
    return toolbar[:commands] if toolbar && toolbar[:commands]
    
    # 如果两个地方都没找到，返回一个空数组
    return []
  end

  def self.generate_icon_uri(relative_path)
    return nil unless relative_path && !relative_path.empty?
    
    absolute_icon_path = File.join(DATA_DIR, relative_path)
    if File.exist?(absolute_icon_path)
      image_data = File.binread(absolute_icon_path)
      base64_string = Base64.strict_encode64(image_data)
      return "data:image/png;base64,#{base64_string}"
    end
    
    return nil # 如果文件不存在，返回nil
  end

  def self.delete_dock_bar(dock_id)
    configs = get_configs
    dock_bar_to_delete = (configs['dock_bars'] || []).find { |db| db['id'] == dock_id }
    return unless dock_bar_to_delete
  
    # 1. 从所有停靠槽的分配记录中移除该DOCK栏
    if configs['dock_bar_assignments']
      configs['dock_bar_assignments'].each_value do |id_array|
        id_array.delete(dock_id)
      end
    end
  
    # 2. 从主列表中移除该DOCK栏的定义
    (configs['dock_bars'] || []).reject! { |db| db['id'] == dock_id }
    
    # ==================================================================================
    # ▼▼▼ 核心修正：增强清理逻辑，使其能处理所有类型的子项目 ▼▼▼
    # ==================================================================================
    # 3. 遍历被删除DOCK栏中的每一个项目，执行对应的清理检查
    (dock_bar_to_delete['commands'] || []).each do |item|
      next unless item # 跳过可能的nil

      # a. 对所有带图标的项目（普通命令、文件夹封面）执行图标文件清理检查
      check_and_delete_icon(item['icon_path'], configs) if item['icon_path']
      
      # b. 如果项目是文件夹，则额外执行文件夹专属的缓存目录清理检查
      if item['type'] == 'folder' && item['source_toolbar_name']
        cleanup_folder_cache(item['source_toolbar_name'], configs)
      end
    end
    # ======================== ▲▲▲ 核心修正结束 ▲▲▲ ========================
  
    # 4. 保存最终的配置
    save_configs(configs)
    refresh_ui(false)
  end

  def self.create_dock_bar(name)
    configs = get_configs
    configs['dock_bars'] ||= [] # 如果不存在，则初始化
    
    new_dock_bar = {
      'id' => SecureRandom.uuid,
      'name' => name,
      'commands' => [] # 初始为空，包含普通图标和文件夹
    }
  
    configs['dock_bars'] << new_dock_bar
    save_configs(configs)
  end
  
  # 辅助方法：生成唯一的名称
  # @param base_name [String] 用户输入的原始名称
  # @param existing_names [Array<String>] 已存在的名称列表
  # @return [String] 一个保证唯一的名称
  def self.generate_unique_name(base_name, existing_names)
    # 为了进行不区分大小写的比较，我们将所有已存在名称转为小写存入一个Set，提高查找效率
    existing_set = Set.new(existing_names.map(&:downcase))
  
    # 首先检查原始名称是否可用
    return base_name unless existing_set.include?(base_name.downcase)
  
    # 如果原始名称已存在，则开始尝试添加数字后缀
    counter = 2
    loop do
      new_name = "#{base_name}#{counter}"
      # 如果拼接了数字后缀的新名称不存在，则返回这个新名称
      return new_name unless existing_set.include?(new_name.downcase)
      # 如果还存在，则继续增加数字
      counter += 1
    end
  end
  # --- 【新增结束】 ---

  # 分组管理方法
  def self.create_plugin_group(group_name)
    configs = get_configs
    configs['plugin_groups'] ||= {}

    if configs['plugin_groups'].key?(group_name)
      UI.messagebox("分组 \"#{group_name}\" 已存在。", MB_OK, "创建失败")
      return
    end

    configs['plugin_groups'][group_name] = []
    save_configs(configs)



    # 立即刷新UI以显示新分组
    refresh_ui(false)

    # 延迟显示成功消息，确保UI已更新
    UI.start_timer(0.1, false) {
      UI.messagebox("分组 \"#{group_name}\" 创建成功。", MB_OK, "创建成功")
    }
  end

  def self.rename_plugin_group(old_name, new_name)
    # 保护默认分组不被重命名
    if old_name == '默认分组'
      UI.messagebox("默认分组不能重命名。", MB_OK, "重命名失败")
      return
    end

    configs = get_configs
    configs['plugin_groups'] ||= {}

    unless configs['plugin_groups'].key?(old_name)
      UI.messagebox("分组 \"#{old_name}\" 不存在。", MB_OK, "重命名失败")
      return
    end

    if configs['plugin_groups'].key?(new_name)
      UI.messagebox("分组 \"#{new_name}\" 已存在。", MB_OK, "重命名失败")
      return
    end

    # 重命名分组
    configs['plugin_groups'][new_name] = configs['plugin_groups'].delete(old_name)
    save_configs(configs)
    refresh_ui(false)
    UI.messagebox("分组已重命名为 \"#{new_name}\"。", MB_OK, "重命名成功")
  end

  def self.delete_plugin_group(group_name)
    # 保护默认分组不被删除
    if group_name == '默认分组'
      UI.messagebox("默认分组不能删除。", MB_OK, "删除失败")
      return
    end

    configs = get_configs
    configs['plugin_groups'] ||= {}

    unless configs['plugin_groups'].key?(group_name)
      UI.messagebox("分组 \"#{group_name}\" 不存在。", MB_OK, "删除失败")
      return
    end

    # 删除分组
    configs['plugin_groups'].delete(group_name)
    save_configs(configs)
    refresh_ui(false)
    UI.messagebox("分组 \"#{group_name}\" 已删除。", MB_OK, "删除成功")
  end

  # 工具栏管理方法
  def self.toggle_toolbar_visibility(toolbar_name)
    begin
      # 尝试多种方式查找工具栏
      toolbar = nil

      # 方法1：直接通过名称查找
      begin
        toolbar = UI.toolbar(toolbar_name)
      rescue
        # 如果直接查找失败，尝试其他方法
      end

      # 方法2：遍历所有工具栏查找匹配的
      if toolbar.nil?
        UI.toolbars.each do |tb|
          if tb.name == toolbar_name || tb.name.include?(toolbar_name) || toolbar_name.include?(tb.name)
            toolbar = tb
            break
          end
        end
      end

      if toolbar
        # 切换工具栏的可见性
        current_state = toolbar.visible?
        if current_state
          toolbar.hide
          UI.messagebox("工具栏 \"#{toolbar.name}\" 已隐藏。", MB_OK, "操作成功")
        else
          toolbar.show
          UI.messagebox("工具栏 \"#{toolbar.name}\" 已显示。", MB_OK, "操作成功")
        end

        puts "Toolbar '#{toolbar.name}' visibility changed from #{current_state} to #{!current_state}"

        # 刷新UI以更新状态指示器
        refresh_ui(false)
      else
        # 显示所有可用的工具栏名称以便调试
        available_toolbars = UI.toolbars.map(&:name).join(", ")
        puts "Available toolbars: #{available_toolbars}"
        UI.messagebox("未找到工具栏 \"#{toolbar_name}\"。\n\n可用的工具栏：\n#{available_toolbars}", MB_OK, "操作失败")
      end
    rescue => e
      puts "Error toggling toolbar visibility: #{e.message}"
      UI.messagebox("切换工具栏可见性时发生错误：#{e.message}", MB_OK, "操作失败")
    end
  end

  def self.move_toolbar_to_group(toolbar_name, group_name)
    configs = get_configs
    configs['plugin_groups'] ||= {}

    # 确保目标分组存在
    unless configs['plugin_groups'].key?(group_name)
      UI.messagebox("目标分组 \"#{group_name}\" 不存在。", MB_OK, "移动失败")
      return
    end

    # 从所有分组中移除该工具栏
    configs['plugin_groups'].each do |group, toolbars|
      toolbars.delete_if { |name| name.include?(toolbar_name) || toolbar_name.include?(name) }
    end

    # 添加到目标分组
    configs['plugin_groups'][group_name] ||= []
    unless configs['plugin_groups'][group_name].include?(toolbar_name)
      configs['plugin_groups'][group_name] << toolbar_name
    end

    save_configs(configs)
    refresh_ui(false)
    UI.messagebox("工具栏 \"#{toolbar_name}\" 已移动到分组 \"#{group_name}\"。", MB_OK, "移动成功")
  end

  def self.uninstall_plugin(toolbar_name)
    begin
      # 这是一个危险操作，需要谨慎处理
      # 首先尝试找到插件文件
      plugin_files = []

      # 搜索插件目录
      plugins_dir = Sketchup.find_support_file("Plugins")
      if plugins_dir && Dir.exist?(plugins_dir)
        # 搜索可能的插件文件
        Dir.glob(File.join(plugins_dir, "**", "*")).each do |file|
          if File.basename(file, ".*").downcase.include?(toolbar_name.downcase) ||
             toolbar_name.downcase.include?(File.basename(file, ".*").downcase)
            plugin_files << file
          end
        end
      end

      if plugin_files.empty?
        UI.messagebox("未找到与 \"#{toolbar_name}\" 相关的插件文件。\n\n请手动删除插件文件。", MB_OK, "卸载失败")
        return
      end

      # 显示找到的文件列表
      file_list = plugin_files.map { |f| File.basename(f) }.join("\n")
      result = UI.messagebox("找到以下相关文件：\n\n#{file_list}\n\n确定要删除这些文件吗？", MB_YESNO, "确认卸载")

      if result == IDYES
        deleted_files = []
        failed_files = []

        plugin_files.each do |file|
          begin
            if File.exist?(file)
              File.delete(file)
              deleted_files << File.basename(file)
            end
          rescue => e
            failed_files << "#{File.basename(file)} (#{e.message})"
          end
        end

        message = "卸载完成！\n\n"
        message += "已删除文件：\n#{deleted_files.join("\n")}\n\n" unless deleted_files.empty?
        message += "删除失败：\n#{failed_files.join("\n")}\n\n" unless failed_files.empty?
        message += "请重启SketchUp以完全卸载插件。"

        UI.messagebox(message, MB_OK, "卸载结果")
      end

    rescue => e
      puts "Error uninstalling plugin: #{e.message}"
      UI.messagebox("卸载插件时发生错误：#{e.message}", MB_OK, "卸载失败")
    end
  end

  # 显示设置管理方法
  def self.set_separator_display(show_separators)
    configs = get_configs
    configs['display_settings'] ||= {}
    configs['display_settings']['show_separators'] = show_separators
    save_configs(configs)

    # 刷新UI以应用新设置
    refresh_ui(false)

    puts "Separator display setting changed to: #{show_separators}"
  end

  # 拖拽排序管理方法
  def self.reorder_plugin_groups(source_group, target_group, is_after)
    configs = get_configs
    configs['plugin_groups'] ||= {}

    # 获取当前分组顺序
    current_groups = configs['plugin_groups'].keys

    # 移除源分组
    current_groups.delete(source_group)

    # 找到目标分组的位置
    target_index = current_groups.index(target_group)
    if target_index
      insert_index = is_after ? target_index + 1 : target_index
      current_groups.insert(insert_index, source_group)
    else
      current_groups << source_group
    end

    # 重新构建分组配置
    new_groups = {}
    current_groups.each do |group_name|
      new_groups[group_name] = configs['plugin_groups'][group_name] || []
    end

    configs['plugin_groups'] = new_groups
    save_configs(configs)
    refresh_ui(false)

    puts "Reordered groups: #{source_group} -> #{target_group} (after: #{is_after})"
  end

  def self.reorder_toolbars_in_group(source_toolbar, target_toolbar, group_name, is_after)
    configs = get_configs
    configs['plugin_groups'] ||= {}
    configs['plugin_groups'][group_name] ||= []

    toolbars = configs['plugin_groups'][group_name]

    # 移除源工具栏
    toolbars.delete(source_toolbar)

    # 找到目标工具栏的位置
    target_index = toolbars.index(target_toolbar)
    if target_index
      insert_index = is_after ? target_index + 1 : target_index
      toolbars.insert(insert_index, source_toolbar)
    else
      toolbars << source_toolbar
    end

    save_configs(configs)
    refresh_ui(false)

    puts "Reordered toolbars in group #{group_name}: #{source_toolbar} -> #{target_toolbar} (after: #{is_after})"
  end

  def self.move_toolbar_between_groups(toolbar_name, source_group, target_group, target_toolbar, is_after)
    configs = get_configs
    configs['plugin_groups'] ||= {}
    configs['plugin_groups'][source_group] ||= []
    configs['plugin_groups'][target_group] ||= []

    # 从源分组移除
    configs['plugin_groups'][source_group].delete(toolbar_name)

    # 添加到目标分组
    target_toolbars = configs['plugin_groups'][target_group]
    target_index = target_toolbars.index(target_toolbar)

    if target_index
      insert_index = is_after ? target_index + 1 : target_index
      target_toolbars.insert(insert_index, toolbar_name)
    else
      target_toolbars << toolbar_name
    end

    save_configs(configs)
    refresh_ui(false)

    puts "Moved toolbar #{toolbar_name} from #{source_group} to #{target_group}"
  end

  def self.move_toolbar_to_group_position(toolbar_name, source_group, target_group, is_after)
    configs = get_configs
    configs['plugin_groups'] ||= {}
    configs['plugin_groups'][source_group] ||= []
    configs['plugin_groups'][target_group] ||= []

    # 从源分组移除
    configs['plugin_groups'][source_group].delete(toolbar_name)

    # 添加到目标分组的开头或结尾
    if is_after
      configs['plugin_groups'][target_group] << toolbar_name
    else
      configs['plugin_groups'][target_group].unshift(toolbar_name)
    end

    save_configs(configs)
    refresh_ui(false)

    puts "Moved toolbar #{toolbar_name} to #{target_group} (#{is_after ? 'end' : 'beginning'})"
  end
end

# ==========================================================================
# 一个专门用于响应DOCK栏点击的、轻量级的临时工具（包含自动缓存逻辑）
# ==========================================================================
class ClickableDockTool
  # 初始化时，我们只关心要执行命令的唯一ID
  def initialize(unique_id)
    @unique_id = unique_id
  end

  # 当工具被激活时，改变鼠标光标
  def activate
    
  end

  def deactivate(view)
    # 此处无需执行任何操作，因为工具是临时的
  end

  # 当用户点击鼠标左键时
  def onLButtonDown(flags, x, y, view)
    # 调用包含完整缓存逻辑的执行方法
    execute_command_by_id(@unique_id)
    # 无论成功与否，任务完成，立即将自己从工具栈中弹出
    Sketchup.active_model.tools.pop_tool
  end

  private

  # 这是一个独立的、功能完整的命令执行器
  def execute_command_by_id(unique_id)
    return unless unique_id
    
    # 第一次尝试从缓存中获取命令
    original_cmd = OneToolbarCreator.get_live_command(unique_id)
    
    # 如果找不到，则启动自动缓存，然后再次尝试
    if original_cmd.nil?
      puts "[ONE Toolbar] Command not in cache. Recaching..." # 保留一个有用的提示
      OneToolbarCreator.cache_all_command_data
      original_cmd = OneToolbarCreator.get_live_command(unique_id)
    end
    
    # 最终检查并执行命令
    if original_cmd && original_cmd.proc.is_a?(Proc)
      UI.start_timer(0, false) { original_cmd.proc.call }
    else
      # 如果两次尝试后依然失败，则提示用户
      UI.messagebox("命令 '#{unique_id}' 未加载或无法执行。", MB_OK, "命令执行失败")
    end
  end
end

# ==========================================================================
# 【最终版】DOCK栏切换按钮的临时工具 (使用UI.start_timer确保操作成功)
# ==========================================================================
class ClickableDockSwitchTool
  def initialize(slot_name, switch_region)
    @slot_name = slot_name
    @switch_region = switch_region
  end

  def activate; end
  def deactivate(view); end

  # 【核心修正】将所有逻辑包裹在 UI.start_timer 中
  def onLButtonDown(flags, x, y, view)
    # 使用一个零延迟的定时器来确保工具栈在操作前是稳定的
    UI.start_timer(0, false) do
      # 第一步：关闭任何可能已打开的文件夹弹出窗口
      tools = Sketchup.active_model.tools
      while tools.active_tool.is_a?(OneToolbarCreator::FolderDisplayTool) ||
            tools.active_tool.is_a?(OneToolbarCreator::ClickableFolderLauncherTool)
        tools.pop_tool
      end

      # 第二步：执行Dock栏的切换逻辑
      OneToolbarCreator.cycle_dock_for_slot(@slot_name)
    end
    
    # 当前临时工具的任务已经完成（已经把切换任务交给了定时器），
    # 所以可以立即将自己弹出。
    Sketchup.active_model.tools.pop_tool
  end
  
  def onMouseMove(flags, x, y, view)
    unless @switch_region.contains?(Geom::Point3d.new(x, y, 0))
      Sketchup.active_model.tools.pop_tool
    end
  end

  def onCancel(reason, view)
    Sketchup.active_model.tools.pop_tool
  end
end

# 【修改】自动隐藏按钮的临时工具，现在调用新的方法
class ClickableDockAutoHideTool
  def initialize(slot_name, region)
    @slot_name = slot_name
    @region = region
  end

  def activate; end
  def deactivate(view); end

  def onLButtonDown(flags, x, y, view)
    # 调用新的、只切换行为的方法
    OneToolbarCreator.toggle_auto_hide_behavior(@slot_name)
    Sketchup.active_model.tools.pop_tool
  end
  
  def onMouseMove(flags, x, y, view); unless @region.contains?(Geom::Point3d.new(x, y, 0)); Sketchup.active_model.tools.pop_tool; end; end
  def onCancel(reason, view); Sketchup.active_model.tools.pop_tool; end
end