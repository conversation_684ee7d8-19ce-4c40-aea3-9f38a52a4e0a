<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>大锋插件管理器V3.0</title>
  <!-- CSS更新时间戳: 2024-12-19 15:30 -->
  <style>
    /* ===== 基础重置 ===== */
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body, html {
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
      background-color: #ffffff;
      color: #333333;
      overflow: hidden;
    }

    /* ===== 统一容器 ===== */
    .app {
      display: flex;
      flex-direction: column;
      height: 100vh;
    }

    .content {
      flex: 1;
      overflow-y: auto;
      padding: 20px; /* 统一的padding，所有内容都在这个容器内 */
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    .content::-webkit-scrollbar {
      display: none;
    }

    /* ===== 头部信息 ===== */
    .header-info {
      display: flex;
      align-items: center;
      gap: 16px;
      margin: 0 -20px 0 -20px; /* 延伸到边缘，与搜索框一致 */
      padding: 12px 12px 12px 12px; /* 使用与搜索框相同的12px内边距 */
      border-bottom: 1px solid #e0e0e0;
    }

    .header-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #4a90e2, #357abd);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: bold;
      font-size: 18px;
    }

    .header-text h1 {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .header-text p {
      font-size: 12px;
      color: #666;
    }

    /* ===== 搜索框 ===== */
    .search-section {
      /* 完全延伸到界面边缘，无任何间距 */
      margin: 0 -20px 8px -20px; /* 负margin抵消容器padding，底部间距8px */
      padding: 6px 12px; /* 搜索区域的内边距，更紧凑 */
      background: #f8f9fa; /* 背景色 */
      border-bottom: 1px solid #e0e0e0;
    }

    .search-container {
      position: relative;
      width: 100%; /* 明确设置宽度 */
      display: block; /* 确保块级元素 */
    }

    .search-input {
      width: 100%;
      padding: 6px 32px 6px 12px; /* 更紧凑的padding，右侧留空给图标 */
      border: 1px solid #e0e0e0;
      border-radius: 16px; /* 稍微减小圆角 */
      background: white;
      font-size: 13px; /* 稍微减小字体 */
      outline: none;
      transition: all 0.2s ease;
      box-sizing: border-box;
    }

    .search-input:focus {
      border-color: #4a90e2;
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
    }

    .search-icon {
      position: absolute;
      right: 10px; /* 调整到合适位置 */
      top: 50%;
      transform: translateY(-50%);
      color: #999;
      font-size: 13px; /* 与输入框字体大小匹配 */
      pointer-events: none;
    }

    /* ===== 分组和工具栏 ===== */
    .group {
      margin-bottom: 2px;
    }

    .group-header {
      background: #e8e8e8;
      text-align: center;
      font-weight: 600;
      font-size: 13px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      user-select: none;

      /* 让背景延伸到容器边缘，内容与搜索框对齐 */
      margin: 0 -20px;
      padding: 12px 12px; /* 统一设置：上下12px，左右12px */
    }

    .group-header:hover {
      background: #d0d0d0;
    }

    .toolbar {
      border-bottom: 1px solid #e0e0e0;
    }

    .toolbar-header {
      background: #e8e8e8;
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      /* 让背景延伸到容器边缘，内容与搜索框对齐 */
      margin: 0 -20px;
      padding: 8px 12px; /* 统一设置：上下8px，左右12px */
    }

    .toolbar-header:hover {
      background: #d0d0d0;
    }

    .toolbar-name {
      flex: 1;
    }

    .toolbar-status {
      color: #28a745;
      font-size: 10px;
    }

    .expand-icon {
      font-size: 10px;
      transition: transform 0.2s ease;
    }

    .toolbar.expanded .expand-icon {
      transform: rotate(90deg);
    }

    /* ===== 图标区域 ===== */
    .toolbar-content {
      background: #f0f0f0;
      display: none;

      /* 让背景延伸到容器边缘，内容与搜索框对齐 */
      margin: 0 -20px;
      padding: 12px 12px !important; /* 使用!important确保生效 */
    }

    /* 更具体的选择器确保padding生效 */
    .toolbar.expanded .toolbar-content {
      display: block;
      padding: 12px 12px !important; /* 确保展开时padding也生效 */
    }



    .icon-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
    }

    .icon-item {
      width: 32px;
      height: 32px;
      border-radius: 4px;
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .icon-item:hover {
      background: rgba(74, 144, 226, 0.1);
      transform: translateY(-1px);
    }

    .icon-item img {
      width: 24px;
      height: 24px;
    }

    /* ===== 暗色模式 ===== */
    body.dark-mode {
      background-color: #2d2d2d;
      color: #e0e0e0;
    }

    body.dark-mode .content {
      background-color: #2d2d2d;
    }

    body.dark-mode .header-info {
      border-bottom-color: #555;
    }

    body.dark-mode .search-section {
      background: #3a3a3a;
      border-bottom-color: #555;
    }

    body.dark-mode .search-input {
      background: #2a2a2a;
      border-color: #555;
      color: #e0e0e0;
    }

    body.dark-mode .search-input::placeholder {
      color: #999;
    }

    body.dark-mode .search-input:focus {
      border-color: #4a90e2;
      box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.3);
    }

    body.dark-mode .group-header {
      background: #3a3a3a;
      color: #e0e0e0;
    }

    body.dark-mode .group-header:hover {
      background: #4a4a4a;
    }

    body.dark-mode .toolbar-header {
      background: #3a3a3a;
      color: #e0e0e0;
    }

    body.dark-mode .toolbar-header:hover {
      background: #4a4a4a;
    }

    body.dark-mode .toolbar-content {
      background: #2a2a2a;
    }

    body.dark-mode .toolbar {
      border-bottom-color: #555;
    }

    /* ===== 空状态 ===== */
    .empty-message {
      text-align: center;
      padding: 40px 0;
      color: #999;
      font-style: italic;
    }

    /* ===== 调试样式（临时） ===== */
    .debug-mode .content {
      border: 2px solid red;
      position: relative;
    }

    .debug-mode .content::before {
      content: '';
      position: absolute;
      top: 0;
      left: 20px;
      right: 20px;
      bottom: 0;
      border: 1px dashed blue;
      pointer-events: none;
    }

    .debug-mode .group-header {
      border-top: 1px solid green;
      border-bottom: 1px solid green;
    }

    .debug-mode .icon-grid {
      border: 1px solid orange;
    }

    .debug-mode .toolbar-content {
      border: 3px solid purple !important;
      background: rgba(255, 0, 255, 0.1) !important;
    }

    /* 添加padding可视化 */
    .debug-mode .toolbar-content::before {
      content: 'padding区域';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 12px;
      background: rgba(255, 255, 0, 0.5);
      font-size: 10px;
      z-index: 100;
    }

    /* 添加中心线参考 */
    .debug-mode .content::after {
      content: '';
      position: absolute;
      top: 0;
      left: 50%;
      bottom: 0;
      width: 1px;
      background: red;
      pointer-events: none;
      z-index: 1000;
    }

    /* 搜索框调试 */
    .debug-mode .search-container {
      border: 2px solid purple;
    }

    /* 头部信息调试 */
    .debug-mode .header-info {
      border: 2px solid cyan;
    }
  </style>
</head>
<body>
  <div class="app">
    <div class="content">
      <!-- 头部信息 -->
      <div class="header-info">
        <div class="header-icon">大</div>
        <div class="header-text">
          <h1>大锋插件管理器V3.0</h1>
          <p><EMAIL></p>
        </div>
        <button onclick="toggleDebug()" style="margin-left: auto; padding: 4px 8px; font-size: 12px;">调试</button>
      </div>

      <!-- 搜索框 -->
      <div class="search-section">
        <div class="search-container">
          <input type="text" class="search-input" placeholder="搜索插件..."
                 oninput="handleSearchInput()" onkeydown="handleSearchKeydown(event)">
          <span class="search-icon">⌕</span>
        </div>
      </div>

      <!-- 参考线（调试用） -->
      <div id="reference-line" style="display: none; height: 2px; background: red; margin: 10px 0; position: relative;">
        <span style="position: absolute; left: 50%; transform: translateX(-50%); top: -15px; font-size: 10px; color: red;">中心线</span>
      </div>

      <!-- 测试居中元素 -->
      <div id="test-center" style="display: none; background: yellow; padding: 8px 0; text-align: center; margin: 0 -20px; padding-left: 12px; padding-right: 12px; font-weight: 600; font-size: 13px;">
        测试居中文字
      </div>

      <!-- 内容区域 -->
      <div id="main-content">
        <!-- 内容将通过JavaScript动态生成 -->
      </div>
    </div>
  </div>

  <script>
    // 示例数据
    const sampleData = {
      groups: [
        {
          name: "默认分组",
          toolbars: [
            {
              name: "综合类大插件集",
              status: "●",
              expanded: true,
              icons: [
                { name: "工具1", icon: "🔧" },
                { name: "工具2", icon: "⚙️" },
                { name: "工具3", icon: "🔨" },
                { name: "工具4", icon: "📐" },
                { name: "工具5", icon: "📏" },
                { name: "工具6", icon: "🎯" },
                { name: "工具7", icon: "🎨" },
                { name: "工具8", icon: "🖼️" }
              ]
            }
          ]
        }
      ]
    };

    // 搜索功能
    function handleSearchInput() {
      renderContent();
    }

    function handleSearchKeydown(event) {
      if (event.key === 'Escape') {
        document.querySelector('.search-input').value = '';
        renderContent();
      }
    }

    // 切换工具栏展开/收起
    function toggleToolbar(element) {
      const toolbar = element.closest('.toolbar');
      toolbar.classList.toggle('expanded');
    }

    // 图标点击事件
    function onIconClick(iconName) {
      console.log('点击了图标:', iconName);
    }

    // 调试模式切换
    function toggleDebug() {
      document.body.classList.toggle('debug-mode');
      const referenceLine = document.getElementById('reference-line');
      const testCenter = document.getElementById('test-center');

      if (document.body.classList.contains('debug-mode')) {
        referenceLine.style.display = 'block';
        testCenter.style.display = 'block';
      } else {
        referenceLine.style.display = 'none';
        testCenter.style.display = 'none';
      }
    }

    // 渲染内容
    function renderContent() {
      const searchTerm = document.querySelector('.search-input').value.toLowerCase();
      const mainContent = document.getElementById('main-content');

      let html = '';

      sampleData.groups.forEach(group => {
        const filteredToolbars = group.toolbars.filter(toolbar => {
          if (!searchTerm) return true;
          return toolbar.name.toLowerCase().includes(searchTerm) ||
                 toolbar.icons.some(icon => icon.name.toLowerCase().includes(searchTerm));
        });

        if (filteredToolbars.length === 0) return;

        html += `<div class="group">`;
        html += `<div class="group-header">${group.name}</div>`;

        filteredToolbars.forEach(toolbar => {
          html += `<div class="toolbar ${toolbar.expanded ? 'expanded' : ''}">`;
          html += `<div class="toolbar-header" onclick="toggleToolbar(this)">`;
          html += `<span class="toolbar-name">${toolbar.name}</span>`;
          html += `<span class="toolbar-status">${toolbar.status}</span>`;
          html += `<span class="expand-icon">▶</span>`;
          html += `</div>`;
          html += `<div class="toolbar-content">`;
          html += `<div class="icon-grid">`;

          toolbar.icons.forEach(icon => {
            if (!searchTerm || icon.name.toLowerCase().includes(searchTerm)) {
              html += `<div class="icon-item" title="${icon.name}" onclick="onIconClick('${icon.name}')">`;
              html += `<span style="font-size: 20px;">${icon.icon}</span>`;
              html += `</div>`;
            }
          });

          html += `</div></div></div>`;
        });

        html += `</div>`;
      });

      if (html === '') {
        html = '<div class="empty-message">没有找到匹配的插件</div>';
      }

      mainContent.innerHTML = html;
    }

    // 初始化
    document.addEventListener('DOMContentLoaded', function() {
      renderContent();
    });
  </script>
</body>
</html>
