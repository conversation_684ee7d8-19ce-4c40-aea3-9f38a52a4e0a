# quick_toolbar_tool.rb (已修复语法错误)
require 'fiddle'
require 'fiddle/import'

module OneToolbarCreator
  # =================================================================================
  # 【第1步：新增代码】 添加 Windows API 定义模块
  # =================================================================================
  # =================================================================================
  # 【第1步：修改代码】 更新 User32 模块以使用 Get/SetCursorPos
  # =================================================================================
  if RUBY_PLATFORM =~ /mswin|mingw|cygwin/
    module User32
      extend Fiddle::Importer
      dlload 'user32'

      # extern 'BOOL GetCursorPos(LPPOINT lpPoint)'
      extern 'int GetCursorPos(void*)'

      # extern 'BOOL SetCursorPos(int X, int Y)'
      extern 'int SetCursorPos(int, int)'
    end
  end

  class QuickToolbarTool
    # =================================================================================
    # 1. 初始化与激活 (Initialization & Activation)
    # =================================================================================
    def initialize(disk_id)
      @initial_disk_id = disk_id
      all_configs = OneToolbarCreator.get_configs
      all_disks = all_configs['radial_disks'] || []
      all_groups = all_configs['radial_groups'] || []
      parent_group = all_groups.find { |g| (g['disk_ids'] || []).include?(@initial_disk_id) }
      if parent_group
        disk_map = all_disks.map { |d| [d['id'], d] }.to_h
        @disks_in_group = (parent_group['disk_ids'] || []).map { |id| disk_map[id] }.compact
        @current_disk_index = @disks_in_group.find_index { |d| d['id'] == @initial_disk_id } || 0
      else
        target_disk = all_disks.find { |d| d['id'] == @initial_disk_id }
        @disks_in_group = target_disk ? [target_disk] : []
        @current_disk_index = 0
      end
      
      @menu_center = nil
      @button_data = []
      @qa_button_data = []
      @qa_bg_points = [] # 【新增】初始化背景点数组
      @texture_ids = {}
      @hovered_cmd_id = nil
      @last_mouse_pos = nil
      # --- 【新增代码】 ---
      @center_button_radius = 40 # 定义中心按钮的半径
      @is_center_hovered = false  # 用于追踪中心按钮是否被悬停
      # --- 【新增结束】 ---
      # 【新增】用于自绘和延迟提示的变量
      @tooltip_text = nil
      @hover_timer = nil
      @current_hover_id = nil
      @center_texture_id = nil # 【新增】
      # 【新增】用于确保模拟事件只触发一次的标志位
      @simulation_fired = false
    end

    # =================================================================================
    # 【最终优化版】替换 activate 方法，实现1像素无延迟移动
    # =================================================================================
    def activate(view = nil)
      @view = view || Sketchup.active_model.active_view
      if @disks_in_group.empty?
        UI.messagebox("错误：找不到圆盘数据。")
        Sketchup.active_model.tools.pop_tool; return
      end

      # 激活时重置状态
      @menu_center = nil
      @last_mouse_pos = nil
      @simulation_fired = false # 每次激活时都重置标志位
      
      # 加载纹理
      load_textures_for_current_disk
    end

    def load_textures_for_current_disk
      return unless current_disk && @view
      # ... (释放旧纹理的逻辑保持不变) ...
      @texture_ids.each_value { |id| @view.release_texture(id) if id }
      @view.release_texture(@center_texture_id) if @center_texture_id
      @texture_ids.clear
      @center_texture_id = nil

      # 【新增】加载中心图标的纹理
      if current_disk['center_icon_path']
        center_icon_full_path = File.join(OneToolbarCreator::DATA_DIR, current_disk['center_icon_path'])
        if File.exist?(center_icon_full_path)
          begin
            @center_texture_id = @view.load_texture(Sketchup::ImageRep.new(center_icon_full_path))
          rescue => e
            puts "中心图标加载失败: #{center_icon_full_path}. Error: #{e.message}"
          end
        end
      end
      all_commands = all_commands_on_disk + all_quick_actions
      all_commands.each do |cmd_def|
        next if cmd_def.nil?
        icon_path = File.join(OneToolbarCreator::DATA_DIR, cmd_def['icon_path'])
        if File.exist?(icon_path)
          begin
            @texture_ids[icon_path] = @view.load_texture(Sketchup::ImageRep.new(icon_path))
          rescue => e
            puts "图标加载失败: #{icon_path}. Error: #{e.message}"
          end
        end
      end
    end

    def deactivate(view)
      @texture_ids.each_value { |id| view.release_texture(id) if id }
      view.release_texture(@center_texture_id) if @center_texture_id # 【新增】
      @texture_ids.clear
      view.invalidate
    end

    # =================================================================================
    # 2. 绘制逻辑 (Drawing Logic)
    # =================================================================================
    def draw(view)
      # --- 【全新的触发逻辑】 ---
      # 检查：仅在Windows平台且模拟事件尚未触发过的情况下执行
      if !@simulation_fired && RUBY_PLATFORM =~ /mswin|mingw|cygwin/
        
        # 1. 立即设置标志位，确保此代码块只执行一次
        @simulation_fired = true
        
        # 2. 启动一个零延迟的计时器。这会将我们的模拟代码块
        #    排队到SketchUp的主事件循环中，在它空闲时执行。
        UI.start_timer(0, false) do
          begin
            # 3. 在这个“纯净”的环境中，执行我们已验证成功的模拟移动代码
            point = Fiddle::Pointer.malloc(8)
            User32.GetCursorPos(point)
            original_x, original_y = point[0, 8].unpack('l<l<')

            User32.SetCursorPos(original_x, original_y - 2)
            sleep(0.01)
            User32.SetCursorPos(original_x, original_y)

          rescue => e
            puts "OneToolbarCreator: 在 timer 中模拟移动时发生错误. Error: #{e.message}"
          end
        end # 计时器结束
      end
      # --- 触发逻辑结束 ---

      # --- 以下是您原有的 draw 方法逻辑，保持不变 ---
      # 注意：我们不再需要在这里 `return`，因为模拟代码在计时器中异步执行，
      # 不会影响本次的绘制流程（虽然本次绘制时 @menu_center 仍为 nil）。
      return unless @menu_center && current_disk
      
      # --- 【核心新增逻辑】---
      # 使用 begin...ensure...end 结构来确保 @menu_center 总能被恢复
      original_center = @menu_center
      begin
        # 1. 在所有绘制计算开始前，获取调整后的中心点
        @menu_center = adjust_center_for_viewport(original_center, view)
        
        # 2. 所有后续的绘制代码都将自动使用这个调整后的、安全的 @menu_center
        precompute_button_data
        detect_hover

        draw_radial_menu(view)
        draw_quick_action_bar(view)
        draw_center_button(view) if has_any_ring_command?

        OneToolbarCreator.draw_custom_tooltip_globally(view, @tooltip_text, @last_mouse_pos)
        
      ensure
        # 3. 无论绘制成功与否，方法结束时都将 @menu_center 恢复为原始值
        #    这可以防止影响下一次事件处理（如 onMouseMove）的逻辑
        @menu_center = original_center
      end
      # --- 新增逻辑结束 ---
    end

    # =================================================================================
    # 3. 输入事件处理 (Input Event Handling)
    # =================================================================================
    def onMouseMove(flags, x, y, view)
      @last_mouse_pos = Geom::Point3d.new(x, y, 0)
      @menu_center ||= @last_mouse_pos
      view.invalidate
    end

    def onLButtonDown(flags, x, y, view)
      return false unless @menu_center
    
      # --- 【新增代码】 ---
      # 优先判断是否点击了中心按钮
      if @is_center_hovered
        # 调用一个新的方法来打开设置面板
        OneToolbarCreator.show_and_focus_disk(current_disk['id'])
        Sketchup.active_model.tools.pop_tool
        return true
      end
      # --- 【新增结束】 ---
    
      # 如果没点中心按钮，则执行原有的逻辑
      combined_buttons = @button_data + @qa_button_data
      clicked_button = combined_buttons.find { |btn| btn[:rect].contains?(Geom::Point3d.new(x, y, 0)) }
      execute_command_by_id(clicked_button[:cmd_def]['unique_id']) if clicked_button
      Sketchup.active_model.tools.pop_tool
      return true
    end

    def onKeyDown(key, repeat, flags, view)
      # a. 首先处理我们内部需要的Tab键，这部分不变
      if key == 9 && @disks_in_group.size > 1
        @current_disk_index = (@current_disk_index + 1) % @disks_in_group.length
        load_textures_for_current_disk
        @button_data.clear; @qa_button_data.clear
        view.invalidate
        return true
      end

      # b. 【核心修改】将按键码转换为我们关心的快捷键字符
      char = key_code_to_shortcut_char(key)

      # c. 判断这个按键是否在我们指定的12个快捷键范围内
      unless char
        # 如果不是 (返回了nil)，说明它不是我们快捷条关心的键 (如 A, B, C, F1等)
        # 我们不处理，必须返回 false，将事件交还给SketchUp系统。
        return false
      end

      # d. 如果是指定的12个快捷键之一，我们就开始处理，并且准备好“拦截”这个事件
      action_to_execute = all_quick_actions.find { |action| action && action['key']&.casecmp(char) == 0 }

      if action_to_execute
        # 如果找到了匹配的动作，则执行它
        execute_command_by_id(action_to_execute['unique_id'])
        Sketchup.active_model.tools.pop_tool
      else
        # 如果按下的键在我们的12个键范围内，但快捷条里没有配置对应功能
        # 我们什么都不做，但依然要告诉SU我们处理了，从而“屏蔽”任何可能冲突的默认行为
        UI.beep
      end

      # e. 【核心修改】只要是这12个键之一 (char 不为 nil)，我们都返回 true，“吃掉”这个事件
      #    这样SketchUp的全局快捷键系统就没机会响应它了。
      return true
    end
    
    def onCancel(reason, view)
       Sketchup.active_model.tools.pop_tool
    end

    # 【更新】根据配置绘制图标或文字
    def draw_center_button(view)
      bg_alpha = @is_center_hovered ? 45 : 30
      view.drawing_color = [0, 0, 0, bg_alpha]
      
      center_points = get_circle_points(@menu_center, @center_button_radius, 50)
      view.draw2d(GL_POLYGON, center_points)
    
      # --- 【核心修改】---
      # 如果中心图标纹理已加载，则绘制图标
      if @center_texture_id
        # 您仍然可以通过修改这个值来控制图标的整体大小
        icon_draw_radius = @center_button_radius * 0.85 

        # 1. 调用我们新增的辅助方法来获取圆形顶点和UV坐标
        points, uvs = OneToolbarCreator.get_textured_circle_data(@menu_center, icon_draw_radius)

        # 2. 设置绘制颜色（通常为白色，以显示原始纹理颜色）
        view.drawing_color = "white"

        # 3. 使用 GL_TRIANGLE_FAN (三角扇) 模式来绘制带纹理的圆形
        view.draw2d(GL_TRIANGLE_FAN, points, uvs: uvs, texture: @center_texture_id)
      else
        # 否则，绘制圆盘名称文字 (旧逻辑)
        disk_name = current_disk['name']
        text_options = {
          font: 'Verdana', size: 10, bold: true, color: [255, 255, 255, 80]
        }
        text_bounds = view.text_bounds(Geom::Point3d.new(0,0,0), disk_name, **text_options)
        text_x = @menu_center.x - (text_bounds.width / 2.0)
        text_y = @menu_center.y - (text_bounds.height / 2.0)
        text_point = Geom::Point3d.new(text_x, text_y, 0)
        view.draw_text(text_point, disk_name, text_options)
      end
      # --- 修改结束 ---
    end

    private # <--- 所有后续方法都是私有的

    def adjust_center_for_viewport(center, view)
      # 1. 根据您的配置，最大一圈的半径是170，按钮自身的半径是20 (size/2)。
      #    我们再加上一点安全边距，设定一个总半径。
      menu_radius = 170 + 20 + 15 # 170(ring) + 20(button) + 15(padding) = 205
      
      # 2. 获取视口的边界
      vp_width = view.vpwidth
      vp_height = view.vpheight
      
      # 3. 复制一份原始中心点，以防修改传入的对象
      adjusted_center = center.clone
      
      # 4. 检查并修正X轴坐标
      if center.x - menu_radius < 0 # 超出左边界
        adjusted_center.x = menu_radius
      elsif center.x + menu_radius > vp_width # 超出右边界
        adjusted_center.x = vp_width - menu_radius
      end
      
      # 5. 检查并修正Y轴坐标
      if center.y - menu_radius < 0 # 超出上边界
        adjusted_center.y = menu_radius
      elsif center.y + menu_radius > vp_height # 超出下边界
        adjusted_center.y = vp_height - menu_radius
      end
      
      return adjusted_center
    end

    def has_any_ring_command?
      # 将所有环的命令平铺成一个数组，去掉nil，然后判断是否为空
      (current_disk['rings'] || []).flatten.compact.any?
    end

    # =================================================================================
    # 4. 辅助方法 (Helper Methods)
    # =================================================================================
    def precompute_button_data
      # --- 圆盘菜单按钮计算 (保持不变) ---
      @button_data.clear
      radii = [70, 120, 170]
      button_size = 40; half_size = button_size / 2.0
      angle_offset = current_disk['start_at_top'] ? -Math::PI / 2 : 0
      (current_disk['rings'] || []).each_with_index do |ring_cmds, r_idx|
        num_slots = current_disk['slot_counts'] ? current_disk['slot_counts'][r_idx] : ring_cmds.length
        next if num_slots == 0
        ring_cmds.each_with_index do |cmd_def, s_idx|
          next if cmd_def.nil?
          angle_rad = angle_offset + 2 * Math::PI * s_idx / num_slots
          center = Geom::Point3d.new(@menu_center.x + radii[r_idx] * Math.cos(angle_rad), @menu_center.y + radii[r_idx] * Math.sin(angle_rad), 0)
          @button_data << { rect: create_bounding_box(center, half_size * 1.05), center: center, cmd_def: cmd_def }
        end
      end
    
      # --- 快捷启动区计算 (已修改) ---
      @qa_button_data.clear
      @qa_bg_points = []
    
      visible_quick_actions = all_quick_actions.compact
      return if visible_quick_actions.empty?
    
      qa_slot_size = 36
      qa_padding = 15 # 按钮之间的垂直间距
      qa_item_height = qa_slot_size + qa_padding
      
      # 计算所有按钮占用的总高度
      total_qa_height = (visible_quick_actions.length * qa_item_height) - qa_padding
      
      start_x = 25
      # 动态计算起始Y坐标，使其在视图中垂直居中
      start_y = (@view.vpheight / 2.0) - (total_qa_height / 2.0)
    
      # 计算背景的几何信息
      bg_padding = 10 # 背景的内边距
      bg_corner_radius = 8 # 背景的圆角半径
      
      bg_bounds = Geom::BoundingBox.new
      bg_bounds.add(
        Geom::Point3d.new(start_x - bg_padding, start_y - bg_padding, 0),
        Geom::Point3d.new(start_x + qa_slot_size + bg_padding, start_y + total_qa_height + bg_padding, 0)
      )
      @qa_bg_points = get_rounded_rect_points(bg_bounds, bg_corner_radius)
    
      # 计算每个按钮的位置
      visible_quick_actions.each_with_index do |action_def, index|
        center_x = start_x + (qa_slot_size / 2.0)
        center_y = start_y + (qa_slot_size / 2.0) + (index * qa_item_height)
        
        center = Geom::Point3d.new(center_x, center_y, 0)
        qa_half_size = qa_slot_size / 2.0
        
        shortcut_label_radius = 10.0 # 增大背景圆形半径，从 9.0 变为 10.0
        
        # 【修改点1：调整 key_pos 的 Y 坐标，使其在代码中指向“右上角”，实际显示为“右下角”】
        # X: 图标中心 + 半径 - shortcut_label_radius (将中心放到右边缘) + 额外向右的偏移量 (保持之前的值)
        # Y: 图标中心 + 半径 - shortcut_label_radius (将中心放到上边缘) + 额外向下的偏移量 (关键反转)
        key_x = center.x + qa_half_size - shortcut_label_radius + 4
        # 将 Y 坐标从 -4 (向上偏移) 变为 +4 (向下偏移), 这样在镜像坐标系中它就会向上移动
        # 如果还是不对，可能需要微调，比如 +2 或 +6
        key_y = center.y + qa_half_size - shortcut_label_radius + 4 # 增加 Y 偏移量，使其在镜像后显示在下方
        
        @qa_button_data << {
          rect: create_bounding_box(center, qa_half_size),
          center: center,
          cmd_def: action_def,
          key_pos: Geom::Point3d.new(key_x, key_y, 0)
        }
      end
    end


    # 【最终修复版】分离“立即高亮”和“延迟提示”的逻辑
    def detect_hover
      # 1. 立即更新高亮状态
      @is_center_hovered = false
      @hovered_cmd_id = nil
      return unless @last_mouse_pos && @menu_center

      if has_any_ring_command? && @menu_center.distance(@last_mouse_pos) <= @center_button_radius
        @is_center_hovered = true
      else
        combined_buttons = @button_data + @qa_button_data
        hovered_button = combined_buttons.find { |btn| btn[:rect].contains?(@last_mouse_pos) }
        @hovered_cmd_id = hovered_button[:cmd_def]['unique_id'] if hovered_button
      end

      # 2. 根据高亮状态，管理延迟提示的计时器
      new_hover_id = nil
      if @is_center_hovered
        new_hover_id = :center_button
      elsif @hovered_cmd_id
        new_hover_id = @hovered_cmd_id
      end

      # 只有在悬停的项发生改变时，才重置计时器
      if new_hover_id != @current_hover_id
        UI.stop_timer(@hover_timer) if @hover_timer
        @hover_timer = nil
        @tooltip_text = nil
        @current_hover_id = new_hover_id

        # 如果当前悬停在一个新项目上，则启动新的计时器
        if @current_hover_id
          @hover_timer = UI.start_timer(1, false) do
            # 1秒后，再次检查以确认鼠标仍在此处
            return unless @current_hover_id == new_hover_id

            # 设置提示文本
            if @current_hover_id == :center_button
              disk_name = current_disk['name']
              @tooltip_text = "打开 #{disk_name} 设置"
            else
              all_commands = all_commands_on_disk + all_quick_actions
              hovered_cmd_def = all_commands.find { |cmd| cmd && cmd['unique_id'] == @current_hover_id }
              @tooltip_text = hovered_cmd_def ? hovered_cmd_def['tooltip'] : ""
            end
            
            # 强制重绘以显示提示
            Sketchup.active_model.active_view.invalidate
          end
        end
      end
    end

    def draw_radial_menu(view)
      radii = [70, 120, 170]; half_size = 20.0
      view.drawing_color = [0, 0, 0, 30]
      (0..2).each { |i| view.draw2d(GL_QUAD_STRIP, get_ring_points(@menu_center, radii[i] - (half_size * 1.05), radii[i] + (half_size * 1.05))) if (current_disk['rings'][i] || []).any? }
      view.line_width = 1; view.line_stipple = ""; view.drawing_color = [255, 255, 255, 50]
      (0..2).each { |i| view.draw2d(GL_LINE_LOOP, get_circle_points(@menu_center, radii[i], 100)) if (current_disk['rings'][i] || []).any? }
      draw_buttons(view, @button_data, half_size)
    end

    def draw_quick_action_bar(view)
      return if @qa_button_data.empty?
    
      # 绘制圆角背景
      unless @qa_bg_points.empty?
        view.drawing_color = [0, 0, 0, 30] # 半透明黑色背景
        view.draw2d(GL_POLYGON, @qa_bg_points)
      end
    
      # 绘制按钮和文字
      draw_buttons(view, @qa_button_data, 21.0)
      
      shortcut_font_size = 12
      shortcut_label_radius = 10.0 # 增大背景圆形半径，从 9.0 变为 10.0
      
      @qa_button_data.each do |btn|
        next unless btn[:cmd_def]['key']
        
        # 调整快捷键背景的透明度
        view.drawing_color = [50, 50, 50, 120] # 降低透明度 (从 200 变为 180，值越小越透明)
        key_bg_pts = get_circle_points(btn[:key_pos], shortcut_label_radius, 20)
        view.draw2d(GL_POLYGON, key_bg_pts)

        # 绘制快捷键文字
        view.drawing_color = [255, 255, 255] # 白色文字
        
        shortcut_char = btn[:cmd_def]['key'].upcase
        
        # 【修改点2：微调文字的居中偏移量，使其在新的圆形背景中更居中】
        # 这些偏移量需要非常精确的调整，确保单字符在 10px 半径的圆中居中
        # 尝试更精细的偏移量
        text_offset_x = -5 # 进一步微调，使其水平居中
        text_offset_y = -9 # 进一步微调，使其垂直居中

        view.draw_text(
          Geom::Point3d.new(btn[:key_pos].x + text_offset_x, btn[:key_pos].y + text_offset_y, 0),
          shortcut_char,
          {font: "Verdana", size: shortcut_font_size, color: [255, 255, 255, 80]}
        )
      end
    end


    def draw_buttons(view, button_list, half_size)
      icon_scale = 0.9; bg_alpha = 210
      button_list.each do |btn|
        is_hovered = (btn[:cmd_def]['unique_id'] == @hovered_cmd_id)
        view.drawing_color = is_hovered ? [200, 225, 255, bg_alpha] : [210, 210, 210, bg_alpha]
        view.draw2d(GL_POLYGON, get_circle_points(btn[:center], half_size * 1.05, 50))

        icon_path = File.join(OneToolbarCreator::DATA_DIR, btn[:cmd_def]['icon_path'])
        if (texture_id = @texture_ids[icon_path])
          # --- 【核心修改】 ---
          # 1. 计算出图标实际绘制的半径
          icon_draw_radius = half_size * icon_scale

          # 2. 调用我们新的全局辅助方法来获取圆形顶点和UV坐标
          points, uvs = OneToolbarCreator.get_textured_circle_data(btn[:center], icon_draw_radius)

          # 3. 使用 GL_TRIANGLE_FAN (三角扇) 模式来绘制带纹理的圆形
          view.drawing_color = "white"
          view.draw2d(GL_TRIANGLE_FAN, points, uvs: uvs, texture: texture_id)
          # --- 【修改结束】 ---
        end
      end
    end
    
    def execute_command_by_id(unique_id)
      return unless unique_id
      original_cmd = OneToolbarCreator.get_live_command(unique_id)
      if original_cmd.nil?
        OneToolbarCreator.cache_all_command_data
        OneToolbarCreator.refresh_ui(false)
        original_cmd = OneToolbarCreator.get_live_command(unique_id)
      end
      if original_cmd&.proc.is_a?(Proc)
        UI.start_timer(0, false) { original_cmd.proc.call }
      else
        UI.messagebox("命令 '#{unique_id}' 未加载或无法执行。", MB_OK)
      end
    end
    
    # 【核心修改】替换旧的 key_to_char 方法
    def key_code_to_shortcut_char(key_code)
      case key_code
      when (48..57) # 数字 0-9
        return (key_code - 48).to_s
      when 189 # 符号 -
        return "-"
      when 187 # 符号 =
        return "="
      else
        return nil # 对于所有其他按键，返回 nil
      end
    end

    def current_disk; @disks_in_group[@current_disk_index]; end
    def all_commands_on_disk; return [] unless current_disk; (current_disk['rings'] || []).flatten.compact; end
    def all_quick_actions; return [] unless current_disk; (current_disk['quick_actions'] || []).compact; end
    
    def get_circle_points(center, radius, num_segments = 36)
      (0..num_segments).map do |i|
        angle = 2 * Math::PI * i / num_segments
        Geom::Point3d.new(center.x + radius * Math.cos(angle), center.y + radius * Math.sin(angle), 0)
      end
    end
    
    def get_ring_points(center, inner_radius, outer_radius, num_segments = 100)
      points = []
      (0..num_segments).each do |i|
        angle = 2 * Math::PI * i / num_segments
        cos_a = Math.cos(angle)
        sin_a = Math.sin(angle)
        points << Geom::Point3d.new(center.x + outer_radius * cos_a, center.y + outer_radius * sin_a, 0)
        points << Geom::Point3d.new(center.x + inner_radius * cos_a, center.y + inner_radius * sin_a, 0)
      end
      points
    end
    
    def create_bounding_box(center, half_size)
      Geom::BoundingBox.new.add(
        Geom::Point3d.new(center.x - half_size, center.y - half_size, 0),
        Geom::Point3d.new(center.x + half_size, center.y + half_size, 0)
      )
    end

    # --- 【请将新方法添加到这里】 ---
    def get_rounded_rect_points(bounds, radius, segments = 8)
      points = []
      # 右上角
      c_tr = Geom::Point3d.new(bounds.max.x - radius, bounds.max.y - radius, 0)
      (0..segments).each do |i|
        angle = Math::PI / 2 * i / segments
        points << Geom::Point3d.new(c_tr.x + radius * Math.cos(angle), c_tr.y + radius * Math.sin(angle), 0)
      end
      # 左上角
      c_tl = Geom::Point3d.new(bounds.min.x + radius, bounds.max.y - radius, 0)
      (0..segments).each do |i|
        angle = Math::PI / 2 + (Math::PI / 2 * i / segments)
        points << Geom::Point3d.new(c_tl.x + radius * Math.cos(angle), c_tl.y + radius * Math.sin(angle), 0)
      end
      # 左下角
      c_bl = Geom::Point3d.new(bounds.min.x + radius, bounds.min.y + radius, 0)
      (0..segments).each do |i|
        angle = Math::PI + (Math::PI / 2 * i / segments)
        points << Geom::Point3d.new(c_bl.x + radius * Math.cos(angle), c_bl.y + radius * Math.sin(angle), 0)
      end
      # 右下角
      c_br = Geom::Point3d.new(bounds.max.x - radius, bounds.min.y + radius, 0)
      (0..segments).each do |i|
        angle = 3 * Math::PI / 2 + (Math::PI / 2 * i / segments)
        points << Geom::Point3d.new(c_br.x + radius * Math.cos(angle), c_br.y + radius * Math.sin(angle), 0)
      end
      points
    end
  end # 【核心修复】补上这个缺失的 'end' 来关闭 class QuickToolbarTool
end