require 'sketchup.rb'
require 'extensions.rb'
require 'digest/md5'

module DF_PluginManager

  # 设置正确的文件路径
  plugin_dir = File.dirname(__FILE__)
  toolbar_creator_path = File.join(plugin_dir, "DF_Plug-in Manager", "toolbar_creator.rb")
  loader_path = File.join(plugin_dir, "DF_Plug-in Manager", "toolbar_creator")

  # 检查文件是否存在并加载
  if File.exist?(toolbar_creator_path)
    begin
      load toolbar_creator_path

      # 只有在成功加载后才注册扩展
      PLUGIN_NAME = "大锋插件管理器V3.0"
      STRING = "大锋插件管理器V3.0.用于创建圆盘菜单,Dock工具栏等快捷方式."

      extension = SketchupExtension.new(OneToolbarCreator::REG_NAME, loader_path)
      extension.creator = "一个工作室"
      extension.version = OneToolbarCreator::VERSION
      extension.copyright = "2024-2025, ONE_Studio."
      extension.description = STRING

      Sketchup.register_extension(extension, true)

    rescue LoadError => e
      puts "Error loading toolbar_creator: #{e.message}"
      puts "File path: #{toolbar_creator_path}"
    rescue => e
      puts "Unexpected error loading toolbar_creator: #{e.message}"
      puts "File path: #{toolbar_creator_path}"
    end
  else
    puts "Error: toolbar_creator.rb not found at: #{toolbar_creator_path}"
  end

end
