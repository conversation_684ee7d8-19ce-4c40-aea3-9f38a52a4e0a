# frozen_string_literal: true

# folder_popup.rb
# 这个文件专门处理DOCK栏中文件夹点击后弹出的二级工具栏的逻辑

if Sketchup.version.to_i >= 23
  module OneToolbarCreator
    #========================================================================
    # 1. 文件夹的临时弹出工具 (FolderDisplayTool)
    #    - 负责绘制临时的二级菜单
    #    - 处理所有临时交互（点击、ESC、外部点击关闭）
    #    - 处理“图钉”点击事件，将自身状态切换为“常驻”
    #========================================================================
    # 【最终版】这个类现在能接收并使用 slot_name 来决定弹出方向
    class FolderDisplayTool
      attr_reader :parent_folder_id
      attr_reader :parent_folder_id, :parent_slot_name
      # 【修改代码】修改 initialize 方法，接收并存储 auto_hide 按钮的区域
      def initialize(folder_data, source_icon_center, main_dock_regions, switch_region, auto_hide_region, slot_name)
        @folder_data = folder_data
        @source_icon_center = source_icon_center
        @is_pinned = false
        @item_regions = []
        @row_backgrounds = []
        @textures = {}
        @pin_texture_id = nil
        @unpin_texture_id = nil
        @last_mouse_pos = nil
        @main_dock_regions = main_dock_regions
        @switch_region = switch_region
        # 【新增代码】存储新按钮的区域
        @auto_hide_region = auto_hide_region
        @slot_name = slot_name
        @parent_folder_id = folder_data[:id]
        @parent_slot_name = slot_name
        # ... 后续代码保持不变 ...
        pinned_data = OneToolbarCreator.get_pinned_folder_data
        if pinned_data && pinned_data['id'] == @folder_data[:id]; @is_pinned = true; end
        @tooltip_text = nil; @hover_timer = nil; @current_hover_id = nil
      end

      def get_bounds
        # 如果菜单还没有计算出任何背景行，则返回nil
        return nil if @row_backgrounds.empty?
        
        # 创建一个新的、空的边界框
        bounds = Geom::BoundingBox.new
        
        # @row_backgrounds 存储了每一行背景的所有顶点坐标
        # 我们将所有这些点都添加到新的边界框中，它会自动计算出能包围所有点的最小矩形
        @row_backgrounds.flatten.each { |point| bounds.add(point) }
        
        # 返回计算好的边界框
        return bounds
      end

      # ... activate, draw, onCancel, deactivate, onMouseMove 等方法保持不变 ...
      def activate
        @view = Sketchup.active_model.active_view
        async_prepare_and_draw
      end
      
      def async_prepare_and_draw
        source_name = @folder_data[:source_toolbar_name]
        success = OneToolbarCreator.ensure_icons_are_cached(source_name)
        unless success
          Sketchup.active_model.tools.pop_tool
          return
        end
        commands = OneToolbarCreator.get_commands_for_folder(source_name)
        load_all_textures(commands)
        precompute_layout(commands)
        @view.invalidate
      end

      def draw(view)
        return if @item_regions.empty?
        @row_backgrounds.each do |bg_points|
          view.drawing_color = [0, 0, 0, 30]
          view.draw2d(GL_POLYGON, bg_points)
        end
        draw_items(view)

        # 【新增】调用全局绘制器来绘制提示
        OneToolbarCreator.draw_custom_tooltip_globally(view, @tooltip_text, @last_mouse_pos)
      end
      
      # 2. 修改 onLButtonDown 方法，使其能处理所有热区
      def onLButtonDown(flags, x, y, view)
        click_point = Geom::Point3d.new(x, y, 0)
        
        popup_region = @item_regions.find { |r| r[:rect].contains?(click_point) }
        if popup_region
          case popup_region[:type]
          when :command; OneToolbarCreator.execute_command_by_id(popup_region[:item][:unique_id]); Sketchup.active_model.tools.pop_tool unless @is_pinned
          when :pin; @is_pinned = !@is_pinned; OneToolbarCreator.set_pinned_folder(@is_pinned ? @folder_data : nil); view.invalidate
          end
          return
        end

        if @switch_region && @switch_region.contains?(click_point)
          Sketchup.active_model.tools.pop_tool unless @is_pinned
          OneToolbarCreator.cycle_dock_for_slot(@slot_name)
          return
        end
        
        # 【新增代码】添加对自动隐藏按钮点击的代理处理
        if @auto_hide_region && @auto_hide_region.contains?(click_point)
          Sketchup.active_model.tools.pop_tool unless @is_pinned
          OneToolbarCreator.toggle_auto_hide_for_slot(@slot_name)
          return
        end

        # ... 后续的点击处理逻辑保持不变 ...
        main_dock_region = @main_dock_regions.find { |r| r[:rect].contains?(click_point) }
        if main_dock_region
          Sketchup.active_model.tools.pop_tool unless @is_pinned
          item_data = main_dock_region[:item]
          case item_data[:type] 
          when "command"; OneToolbarCreator.execute_command_by_id(item_data[:unique_id])
          when "folder"
            if item_data[:id] != @folder_data[:id]
              UI.start_timer(0, false) do
                tools = Sketchup.active_model.tools
                while tools.active_tool.is_a?(OneToolbarCreator::FolderDisplayTool) || tools.active_tool.is_a?(OneToolbarCreator::ClickableFolderLauncherTool); tools.pop_tool; end
                # 【修改代码】实例化新工具时，传递 nil 作为 auto_hide_region，因为我们无法轻易获取到新工具的这个区域
                new_folder_tool = OneToolbarCreator::FolderDisplayTool.new(item_data, main_dock_region[:center], @main_dock_regions, @switch_region, nil, @slot_name)
                tools.push_tool(new_folder_tool)
              end
            end
          end
          return
        end
        
        Sketchup.active_model.tools.pop_tool unless @is_pinned
      end

      def onCancel(reason, view)
        Sketchup.active_model.tools.pop_tool unless @is_pinned
      end

      def deactivate(view)
        release_textures
        pinned_data = OneToolbarCreator.get_pinned_folder_data
        if pinned_data && pinned_data['id'] != @folder_data[:id]
           UI.start_timer(0, false) { OneToolbarCreator.show_folder_overlay(pinned_data) }
        end
        view.invalidate
      end

      def onMouseMove(flags, x, y, view)
        @last_mouse_pos = Geom::Point3d.new(x, y, 0)
    
        # --- 【修改】替换为延迟和自绘提示逻辑 ---
        hovered_region = @item_regions.find { |r| r[:rect].contains?(@last_mouse_pos) }
        new_hover_id = hovered_region ? hovered_region[:item][:unique_id] || hovered_region[:type] : nil
    
        return if new_hover_id == @current_hover_id
    
        UI.stop_timer(@hover_timer) if @hover_timer
        @hover_timer = nil
        @tooltip_text = nil
        @current_hover_id = new_hover_id
    
        if @current_hover_id
          @hover_timer = UI.start_timer(1, false) do
            return unless @current_hover_id == new_hover_id
    
            case hovered_region[:type]
            when :command
              @tooltip_text = hovered_region[:item][:tooltip] || ""
            when :pin
              @tooltip_text = @is_pinned ? "取消固定此面板" : "固定此面板"
            end
            view.invalidate
          end
        end
        # --- 【修改结束】 ---
    
        view.invalidate
      end

      private

      # ... load_all_textures, release_textures 等方法保持不变 ...
      def load_all_textures(commands)
        release_textures
        pin_path = File.join(OneToolbarCreator::PLUGIN_DIR, 'ICON', 'pin.png')
        unpin_path = File.join(OneToolbarCreator::PLUGIN_DIR, 'ICON', 'unpin.png')
        @pin_texture_id = @view.load_texture(Sketchup::ImageRep.new(pin_path)) if File.exist?(pin_path)
        @unpin_texture_id = @view.load_texture(Sketchup::ImageRep.new(unpin_path)) if File.exist?(unpin_path)
        commands.each do |cmd|
          next unless cmd && cmd[:unique_id]
          icon_path = OneToolbarCreator.get_cached_icon_path(cmd[:unique_id], @folder_data[:source_toolbar_name])
          if icon_path && File.exist?(icon_path) && !@textures.key?(cmd[:unique_id])
            begin
              image_rep = Sketchup::ImageRep.new(icon_path)
              @textures[cmd[:unique_id]] = @view.load_texture(image_rep)
            rescue => e
            end
          end
        end
      end
      
      def release_textures
        @textures.each_value { |id| @view.release_texture(id) if id }
        @view.release_texture(@pin_texture_id) if @pin_texture_id
        @view.release_texture(@unpin_texture_id) if @unpin_texture_id
        @textures.clear
      end
      
      # 3. 修改 precompute_layout 方法，实现弹出方向的判断
      # 【最终修复版】统一所有弹出菜单的背景为单一整合模式
      def precompute_layout(items)
        @item_regions.clear
        @row_backgrounds.clear

        icon_size = 44 * 0.9
        item_gap = 8 * 0.9
        bg_padding = 6 * 0.9
        corner_radius = 8 * 0.9

        if @slot_name == 'right'
          # (右侧DOCK栏的逻辑已是风格B，保持不变)
          icons_per_column = 20
          
          item_columns = []
          current_column = []
          icon_count_in_column = 0
          items.each do |item|
            current_column << item
            if item[:type] == 'command'
              icon_count_in_column += 1
              if icon_count_in_column >= icons_per_column
                item_columns << current_column
                current_column = []
                icon_count_in_column = 0
              end
            end
          end
          item_columns << current_column unless current_column.empty?
          
          num_columns = item_columns.length
          column_width = icon_size + (bg_padding * 2)
          popup_width = (column_width * num_columns) + (item_gap * [num_columns - 1, 0].max)

          max_column_height = 0
          item_columns.each do |col_items|
            current_col_height = bg_padding * 2
            col_items.each_with_index do |item, index|
              current_col_height += (item[:type] == 'command' ? icon_size : (item_gap / 3.0))
              current_col_height += item_gap unless index == col_items.length - 1
            end
            max_column_height = [max_column_height, current_col_height].max
          end
          popup_height = max_column_height

          start_x = @source_icon_center.x - popup_width - 35
          start_y = @source_icon_center.y - (popup_height / 2.0)

          bg_bounds = Geom::BoundingBox.new.add(
            Geom::Point3d.new(start_x, start_y, 0),
            Geom::Point3d.new(start_x + popup_width, start_y + popup_height, 0)
          )
          @row_backgrounds << OneToolbarCreator.get_rounded_rect_points(bg_bounds, corner_radius)

          current_column_start_x = start_x + bg_padding
          item_columns.each do |col_items|
            current_y = start_y + bg_padding
            col_items.each_with_index do |item, index|
              item_height = (item[:type] == 'separator') ? (item_gap / 3.0) : icon_size
              center_x = current_column_start_x + (icon_size / 2.0)
              center_y = current_y + (item_height / 2.0)
              center_point = Geom::Point3d.new(center_x, center_y, 0)
              
              rect = Geom::BoundingBox.new.add(
                Geom::Point3d.new(current_column_start_x, current_y, 0),
                Geom::Point3d.new(current_column_start_x + icon_size, current_y + item_height, 0)
              )
              
              region_type = (item[:type] == 'separator') ? :separator : :command
              @item_regions << { type: region_type, item: item, rect: rect, center: center_point }

              current_y += item_height
              current_y += item_gap unless index == col_items.length - 1
            end
            current_column_start_x += column_width + item_gap
          end

        else
          # ==========================================================
          #  分支2: 顶部和底部DOCK栏的【横向】布局逻辑 (已修改为风格B)
          # ==========================================================
          separator_width = item_gap / 3.0
          icons_per_row = 20
          item_rows = []
          current_row = []
          icon_count_in_row = 0
          items.each do |item|
            current_row << item
            if item[:type] == 'command'
              icon_count_in_row += 1
              if icon_count_in_row >= icons_per_row
                item_rows << current_row
                current_row = []
                icon_count_in_row = 0
              end
            end
          end
          item_rows << current_row unless current_row.empty?

          # 【修改点1】计算整个弹出面板的总尺寸
          total_popup_height = (item_rows.length * (icon_size + bg_padding * 2)) + ([item_rows.length - 1, 0].max * item_gap)
          max_row_width = 0
          item_rows.each do |row_items|
            current_row_width = bg_padding * 2
            row_items.each_with_index do |item, index|
              current_row_width += (item[:type] == 'command' ? icon_size : separator_width)
              current_row_width += item_gap unless index == row_items.length - 1
            end
            max_row_width = [max_row_width, current_row_width].max
          end
          
          # 【修改点2】计算总面板的起始坐标
          popup_start_x = @source_icon_center.x - (max_row_width / 2.0)
          popup_start_y = 0
          if @slot_name == 'top'
            popup_start_y = @source_icon_center.y + 35
          else # 'bottom'
            popup_start_y = @source_icon_center.y - total_popup_height - 35
          end

          # 【修改点3】只创建一个大的背景
          total_bg_bounds = Geom::BoundingBox.new.add(
            Geom::Point3d.new(popup_start_x, popup_start_y, 0),
            Geom::Point3d.new(popup_start_x + max_row_width, popup_start_y + total_popup_height, 0)
          )
          @row_backgrounds << OneToolbarCreator.get_rounded_rect_points(total_bg_bounds, corner_radius)
          
          # 【修改点4】循环只用于计算图标位置，不再计算背景
          item_rows.each_with_index do |row_items, row_index|
            row_height = icon_size + bg_padding * 2
            row_y_offset = row_index * (row_height + item_gap)
            current_x = popup_start_x + bg_padding
            
            row_items.each_with_index do |item, index|
              item_width = (item[:type] == 'separator') ? separator_width : icon_size
              center_y_pos = popup_start_y + row_y_offset + bg_padding + icon_size / 2.0
              center_point = Geom::Point3d.new(current_x + item_width / 2.0, center_y_pos, 0)
              
              rect = Geom::BoundingBox.new.add(
                Geom::Point3d.new(current_x, popup_start_y + row_y_offset + bg_padding, 0),
                Geom::Point3d.new(current_x + item_width, popup_start_y + row_y_offset + bg_padding + icon_size, 0)
              )
              
              region_type = (item[:type] == 'separator') ? :separator : :command
              @item_regions << { type: region_type, item: item, rect: rect, center: center_point }
              current_x += item_width
              current_x += item_gap if index < row_items.length - 1
            end
          end
        end 
        
        # --- 图钉位置计算 (保持不变) ---
        return if @row_backgrounds.empty?
        last_row_bg_bounds = Geom::BoundingBox.new
        @row_backgrounds.last.each { |pt| last_row_bg_bounds.add(pt) }
        
        pin_size = 20
        pin_center = Geom::Point3d.new(last_row_bg_bounds.max.x - pin_size, last_row_bg_bounds.max.y - pin_size, 0)
        pin_rect = Geom::BoundingBox.new.add(
          Geom::Point3d.new(pin_center.x - pin_size / 2.0, pin_center.y - pin_size / 2.0, 0),
          Geom::Point3d.new(pin_center.x + pin_size / 2.0, pin_center.y + pin_size / 2.0, 0)
        )
        @item_regions << { type: :pin, rect: pin_rect, center: pin_center }
      end

      # 【最终修复版】绘制图标和分隔符，并区分垂直/水平样式
      def draw_items(view)
        @item_regions.each do |region|
          if region[:type] == :command
            item_data = region[:item]
            unique_id = item_data[:unique_id]
            is_hovered = @last_mouse_pos ? region[:rect].contains?(@last_mouse_pos) : false
            view.drawing_color = is_hovered ? [200, 225, 255, 210] : [210, 210, 210, 210]
            icon_bg_size = (44 * 0.9) / 2.0
            view.draw2d(GL_POLYGON, OneToolbarCreator.get_circle_points(region[:center], icon_bg_size, 32))
            texture_id = @textures[unique_id]
            # --- 【核心修改点 2: 将图标纹理绘制为圆形】 ---
            if texture_id
              # 计算图标纹理实际绘制的半径
              icon_draw_radius = icon_bg_size * 0.85

              # 调用全局辅助方法，获取绘制圆形纹理所需的顶点和UV坐标
              points, uvs = OneToolbarCreator.get_textured_circle_data(region[:center], icon_draw_radius)

              # 使用 GL_TRIANGLE_FAN 模式将纹理绘制为圆形
              view.drawing_color = "white"
              view.draw2d(GL_TRIANGLE_FAN, points, uvs: uvs, texture: texture_id)
            end
            # --- 【修改结束】 ---
          elsif region[:type] == :separator
            view.drawing_color = [255, 255, 255, 50]
            # --- 【核心修改】借鉴主DOCK栏的逻辑 ---
            if @slot_name == 'right'
              # 如果是右侧栏，绘制一条水平线
              sep_y = region[:center].y
              sep_x_start = region[:rect].min.x + 4
              sep_x_end = region[:rect].max.x - 4
              view.draw2d(GL_QUADS, 
                [sep_x_start, sep_y, 0], 
                [sep_x_end, sep_y, 0], 
                [sep_x_end, sep_y + 2, 0], 
                [sep_x_start, sep_y + 2, 0]
              )
            else
              # 否则（顶部/底部），绘制一条垂直线
              center_x = region[:center].x
              rect_min_y = region[:rect].min.y
              rect_height = region[:rect].height
              line_width = 2.0
              half_line_width = line_width / 2.0
              margin = 4.0
              points = [
                  Geom::Point3d.new(center_x - half_line_width, rect_min_y + margin, 0),
                  Geom::Point3d.new(center_x + half_line_width, rect_min_y + margin, 0),
                  Geom::Point3d.new(center_x + half_line_width, rect_min_y + rect_height - margin, 0),
                  Geom::Point3d.new(center_x - half_line_width, rect_min_y + rect_height - margin, 0)
              ]
              view.draw2d(GL_QUADS, points)
            end
            # --- 【修改结束】 ---
          end
        end
        pin_region = @item_regions.find { |r| r[:type] == :pin }
        if pin_region
          texture_id = @is_pinned ? @unpin_texture_id : @pin_texture_id
          if texture_id
            view.drawing_color = "white"
            pin_size = 10
            points = [
              Geom::Point3d.new(pin_region[:center].x - pin_size, pin_region[:center].y - pin_size, 0),
              Geom::Point3d.new(pin_region[:center].x + pin_size, pin_region[:center].y - pin_size, 0),
              Geom::Point3d.new(pin_region[:center].x + pin_size, pin_region[:center].y + pin_size, 0),
              Geom::Point3d.new(pin_region[:center].x - pin_size, pin_region[:center].y + pin_size, 0)
            ]
            uvs = [[0, 0, 0], [1, 0, 0], [1, 1, 0], [0, 1, 0]]
            view.draw2d(GL_QUADS, points, uvs: uvs, texture: texture_id)
          end
        end
      end
      
    end # end FolderDisplayTool

    #========================================================================
    # 2. 文件夹的常驻覆盖层 (FolderOverlay)
    #    - 当文件夹被“图钉”固定后，由它负责常驻显示
    #    - 它的逻辑与主DockOverlay非常相似
    #========================================================================
    class FolderOverlay < Sketchup::Overlay
      def initialize(folder_data, position_data)
        # 每个固定的文件夹Overlay都应该有唯一的ID
        super("com.one_studio.one_toolbar.pinned_folder.#{folder_data[:id]}", "ONE Pinned Folder")
        @folder_data = folder_data
        @position_data = position_data # 存储位置、大小等信息
        @textures = {}
        @item_regions = [] # 存储所有可交互元素的区域信息
        @last_mouse_pos = nil
        @hovered_item_id = nil
      end

      def start
        # ... 实现加载纹理和计算布局的逻辑 ...
      end

      def stop
        # ... 实现释放纹理的逻辑 ...
      end
      
      def draw(view)
        # ... 实现绘制逻辑，与FolderDisplayTool的draw方法类似，但位置是固定的 ...
      end
      
      def onMouseMove(flags, x, y, view)
        # ... 实现悬停检测和激活Clickable...Tool的逻辑，与DockOverlay类似 ...
      end

      # ... 其他方法 ...
    end # end FolderOverlay
    
    #========================================================================
    # 3. 用于启动文件夹工具的、轻量级的临时工具
    #========================================================================
    # 【最终版】这个“信使”工具现在传递所有需要的参数
    class ClickableFolderLauncherTool
      # 接收并存储所有参数
      def initialize(folder_data, source_icon_center, main_dock_regions, switch_region, auto_hide_region, slot_name)
        @folder_data = folder_data
        @source_icon_center = source_icon_center
        @main_dock_regions = main_dock_regions
        @switch_region = switch_region
        # 【新增代码】存储新区域
        @auto_hide_region = auto_hide_region
        @slot_name = slot_name
      end

      def activate; end
      def deactivate(view); end

      # 点击时，将所有参数“接力”传递给真正的 FolderDisplayTool
      def onLButtonDown(flags, x, y, view)
        tools = Sketchup.active_model.tools
        while tools.active_tool.is_a?(OneToolbarCreator::FolderDisplayTool) || tools.active_tool.is_a?(OneToolbarCreator::ClickableFolderLauncherTool)
          tools.pop_tool
        end
        # 【修改代码】将存储的新区域传递给真正的 FolderDisplayTool
        tools.push_tool(FolderDisplayTool.new(@folder_data, @source_icon_center, @main_dock_regions, @switch_region, @auto_hide_region, @slot_name))
      end
      
      def onCancel(reason, view)
        Sketchup.active_model.tools.pop_tool
      end
    end

  end # end module
end # end if