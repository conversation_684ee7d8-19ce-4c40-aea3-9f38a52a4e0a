require 'sketchup.rb'
require 'extensions.rb'
require 'digest/md5'

module DF_PluginManager

  Sketchup.require 'DF_Plug-in Manager/toolbar_creator'
  PLUGIN_NAME = "大锋插件管理器V3.0"
  loader_path = File.join(__dir__, "DF_Plug-in Manager", "toolbar_creator")
  STRING = "大锋插件管理器V3.0.用于创建圆盘菜单,Dock工具栏等快捷方式."

  extension = SketchupExtension.new(OneToolbarCreator::REG_NAME, loader_path)
  extension.creator = "一个工作室"
  extension.version = OneToolbarCreator::VERSION
  extension.copyright = "2024-2025, ONE_Studio."
  extension.description = STRING
  
  Sketchup.register_extension(extension, true)

end
